#ifndef GUI_H
#define GUI_H

#include <gtk/gtk.h>
#include "functions.h"

// Forward declarations of widget globals
extern GtkWidget *login_window;
extern GtkWidget *main_window;
extern GtkBuilder *builder;
extern char current_user_role[10];

// Authentication and main functions
void show_main_window(const char *role);
void on_login_clicked(GtkButton *button, gpointer user_data);
void on_logout_clicked(GtkMenuItem *item, gpointer user_data);
gboolean on_window_delete(GtkWidget *widget, GdkEvent *event, gpointer data);
void on_about_clicked(GtkMenuItem *menuitem, gpointer user_data);
void on_exit_clicked(GtkButton *button, gpointer user_data);
int gui_main(int argc, char *argv[]);

// Utility functions
void show_error_message(const char *message);
void show_success_message(const char *message);
void trim_newline(char *str);

// Patient Management
void on_add_clicked(GtkButton *button, gpointer data);
void on_view_clicked(GtkButton *button, gpointer data);
void on_search_clicked(GtkButton *button, gpointer data);
void on_update_clicked(GtkButton *button, gpointer data);
void on_delete_clicked(GtkButton *button, gpointer data);
void on_room_clicked(GtkButton *button, gpointer data);

// Doctor Management
void on_add_doctor_clicked(GtkButton *button, gpointer data);
void on_view_doctor_clicked(GtkButton *button, gpointer data);
void on_search_doctor_clicked(GtkButton *button, gpointer data);
void on_update_doctor_clicked(GtkButton *button, gpointer data);
void on_delete_doctor_clicked(GtkButton *button, gpointer data);

// Staff Management
void on_add_staff_clicked(GtkButton *button, gpointer data);
void on_view_staff_clicked(GtkButton *button, gpointer data);
void on_search_staff_clicked(GtkButton *button, gpointer data);
void on_update_staff_clicked(GtkButton *button, gpointer data);
void on_delete_staff_clicked(GtkButton *button, gpointer data);

// Ward Management
void on_add_ward_clicked(GtkButton *button, gpointer data);
void on_view_ward_clicked(GtkButton *button, gpointer data);
void on_search_ward_clicked(GtkButton *button, gpointer data);
void on_update_ward_clicked(GtkButton *button, gpointer data);
void on_delete_ward_clicked(GtkButton *button, gpointer data);

#endif /* GUI_H */
