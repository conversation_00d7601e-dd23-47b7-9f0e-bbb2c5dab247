#ifndef GUI_HELPERS_H_
#define GUI_HELPERS_H_

#include <gtk/gtk.h>
#include "functions.h"
#include "dashboard.h"

// Basic GUI callbacks
gboolean on_window_delete(GtkWidget *widget, GdkEvent *event, gpointer data);
void on_add_clicked(GtkButton *button, gpointer user_data);
void on_delete_clicked(GtkButton *button, gpointer user_data);
void on_search_clicked(GtkButton *button, gpointer user_data);
void on_view_clicked(GtkButton *button, gpointer user_data);
void on_update_clicked(GtkButton *button, gpointer user_data);
void on_room_clicked(GtkButton *button, gpointer user_data);
void on_payment_clicked(GtkButton *button, gpointer user_data);
void on_export_csv_clicked(GtkButton *button, gpointer user_data);
void on_dashboard_clicked(GtkButton *button, gpointer user_data);

// Staff management callbacks
void on_add_staff_clicked(GtkButton *button, gpointer user_data);
void on_view_staff_clicked(GtkButton *button, gpointer user_data);
void on_update_staff_clicked(GtkButton *button, gpointer user_data);
void on_delete_staff_clicked(GtkButton *button, gpointer user_data);

// Doctor management callbacks
void on_add_doctor_clicked(GtkButton *button, gpointer user_data);
void on_view_doctor_clicked(GtkButton *button, gpointer user_data);
void on_update_doctor_clicked(GtkButton *button, gpointer user_data);
void on_delete_doctor_clicked(GtkButton *button, gpointer user_data);

// Ward management callbacks
void on_add_ward_clicked(GtkButton *button, gpointer user_data);
void on_view_ward_clicked(GtkButton *button, gpointer user_data);
void on_update_ward_clicked(GtkButton *button, gpointer user_data);
void on_delete_ward_clicked(GtkButton *button, gpointer user_data);

// Helper functions
void show_error_message(const char *message);
void show_success_message(const char *message);
void create_stats_label(const char *text, const char *value, GtkGrid *grid, int row);

// Visualization functions
GtkWidget* create_visualization_area(const DashboardStats *stats);

// Signal connection helper
void connect_signals(GtkWidget *window, GtkBuilder *builder);

#endif // GUI_HELPERS_H_
