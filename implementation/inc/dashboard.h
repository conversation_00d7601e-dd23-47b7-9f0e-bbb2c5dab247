#ifndef DASHBOARD_H_
#define DASHBOARD_H_

#include <gtk/gtk.h>
#include "functions.h"

// File paths for data
#define MEDICAL_RECORDS_FILE "medical_records.dat"
#define BILLING_FILE "billing.dat"
#define INSURANCE_FILE "insurance.dat"
#define APPOINTMENTS_FILE "appointments.dat"
#define WARD_FILE "wards.dat"

// Record structure definitions
typedef struct {
    int id;
    int patient_id;
    int doctor_id;
    char date[11];
    char notes[200];
    float charges;
} MedicalRecord;

typedef struct {
    int id;
    int patient_id;
    char date[20];
    float room_charges;
    float medicine_charges;
    float doctor_fees;
    float test_charges;
    float misc_charges;
    float total_amount;
    float insurance_covered;
    float patient_payable;
    char payment_status[20];
    char payment_method[30];
} BillingRecord;

// Structure definitions
typedef struct {
    char name[50];
    int patients;
    float revenue;
} DepartmentStats;

typedef struct {
    // Patient Statistics
    int total_patients;
    int admitted_patients;
    int outpatients;
    int discharged_today;
    int new_registrations;

    // Doctor Statistics
    int total_doctors;
    int doctors_present;
    int scheduled_appointments;

    // Ward Statistics
    int total_wards;
    int total_beds;
    int occupied_beds;
    int available_beds;
    float bed_occupancy_rate;

    // Financial Statistics
    float total_revenue;
    float pending_payments;
    float insurance_claims;
    float today_collections;

    // Emergency Statistics
    int emergency_cases;
    int critical_patients;

    // Department Performance
    DepartmentStats department_stats[10];
    int department_count;
} DashboardStats;

// Function declarations
void get_current_date(char *date_str);
void get_dashboard_stats(DashboardStats *stats);
void calculate_department_stats(DashboardStats *stats);
void calculate_financial_stats(DashboardStats *stats);
gboolean on_draw_callback(GtkWidget *widget, cairo_t *cr, gpointer data);
GtkWidget* create_visualization_area(const DashboardStats *stats);
int count_records(const char *filename);
double calculate_total_revenue(const char *filename);
int process_uploaded_file(const char *filename);

#endif // DASHBOARD_H_
