#ifndef MANAGEMENT_H
#define MANAGEMENT_H

#include "functions.h"

// Staff Management
extern int add_staff_record(const StaffRecord *rec);
extern int update_staff_record(const StaffRecord *rec);
extern int delete_staff_record(int id);
extern int search_staff_record(int id, StaffRecord *rec);
extern void list_staff_records(void);

// Doctor Management
extern int add_doctor_record(const DoctorRecord *rec);
extern int update_doctor_record(const DoctorRecord *rec);
extern int delete_doctor_record(int id);
extern int search_doctor_record(int id, DoctorRecord *rec);
extern void list_doctor_records(void);

// Ward Management
extern int add_ward_record(const WardRecord *rec);
extern int update_ward_record(const WardRecord *rec);
extern int delete_ward_record(int id);
extern int search_ward_record(int id, WardRecord *rec);
extern void list_ward_records(void);

#endif // MANAGEMENT_H
