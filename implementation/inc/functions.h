#ifndef FUNCTIONS_H_
#define FUNCTIONS_H_

#include <stdio.h>
#include <string.h>
#include <time.h>
#include <ctype.h>
#include <stdbool.h>
#include <stdlib.h>

// File paths - Linux compatible
#define PATIENTS_FILE "patients.csv"
#define ASSIGNMENT_FILE "Assignment.dat"
#define STAFF_FILE "staff.dat"
#define DOCTORS_FILE "doctors.dat"
#define WARDS_FILE "wards.dat"
#define APPOINTMENTS_FILE "appointments.dat"
#define INSURANCE_FILE "insurance.dat"
#define PAYMENTS_FILE "payments.csv"

// Structures - Standardized and consistent
typedef struct {
    int id;
    char full_name[50];
    char address[100];
    int age;
    char gender;
    char contact[15];
    int room;
} PatientRecord;

typedef struct {
    int id;
    char name[50];
    char position[30];
    char department[30];
    char contact[15];
    float salary;
    char joining_date[11];
} StaffRecord;

typedef struct {
    int id;
    char name[50];
    char specialization[50];
    char qualification[50];
    char contact[15];
    float salary;
    char schedule[100];
    int room_no;
} DoctorRecord;

typedef struct {
    int id;
    char name[30];
    int total_beds;
    int occupied_beds;
    char nurse_incharge[50];
    char type[20];
} WardRecord;

typedef struct {
    int id;
    int patient_id;
    int doctor_id;
    char date[11];
    char time[6];
    char status[20];
    char notes[100];
} AppointmentRecord;

typedef struct {
    int patient_id;
    char provider[64];
    char policy_number[32];
    double coverage_amount;
    char expiry_date[11];  // YYYY-MM-DD
} InsuranceRecord;

// Core functionality
void id_valid_check(void);
void add(void);
void remove_record(void);
void admin(void);
void login(void);
void welcome(void);
void edit(void);
char* exitt(void);
void menu(void);
void id_valid(void);
void p_list(void);
void name_valid_check(void);
void find(void);
void find_by_name(void);
void times(void);
void room(void);


// Patient Management
int add_patient_record(const PatientRecord *rec);
int delete_patient_record(int id);
int update_patient_record(const PatientRecord *rec);
int search_patient_record(int id, PatientRecord *rec);

// Staff Management
int add_staff_record(const StaffRecord *rec);
int update_staff_record(const StaffRecord *rec);
int delete_staff_record(int id);
int search_staff_record(int id, StaffRecord *rec);
int get_total_staff(void);

// Doctor Management
int add_doctor_record(const DoctorRecord *rec);
int update_doctor_record(const DoctorRecord *rec);
int delete_doctor_record(int id);
int search_doctor_record(int id, DoctorRecord *rec);
int get_total_doctors(void);

// Ward Management
int add_ward_record(const WardRecord *rec);
int update_ward_record(const WardRecord *rec);
int delete_ward_record(int id);
int search_ward_record(int id, WardRecord *rec);
int get_total_wards(void);
int get_available_beds(void);

// Appointment Management
int add_appointment_record(const AppointmentRecord *rec);
int update_appointment_record(const AppointmentRecord *rec);
int delete_appointment_record(int id);
int search_appointment_record(int id, AppointmentRecord *rec);
int get_total_appointments(void);

// Insurance Management
int get_patient_insurance(int patient_id, InsuranceRecord *insurance);
int update_insurance_record(const InsuranceRecord *insurance);

// Statistics functions
int get_total_patients(void);
int get_patients_admitted_today(void);
int get_total_payments(void);
double get_outstanding_balances(void);

#endif // FUNCTIONS_H
