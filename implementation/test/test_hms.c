
#include "unity.h"
#include "../inc/functions.h"
#include <string.h>

#define PROJECT_NAME "hospital"

/* Required by the unity test framework */
void setUp(){}
/* Required by the unity test framework */
void tearDown(){}

void test_exitt(void){
    TEST_ASSERT_EQUAL_STRING("EXITING THE PROGRAM", exitt());
}

void test_validate_id(void){
    TEST_ASSERT_EQUAL(1, validate_id(123));
    TEST_ASSERT_EQUAL(0, validate_id(-1));
    TEST_ASSERT_EQUAL(0, validate_id(0));
    TEST_ASSERT_EQUAL(0, validate_id(1000000));
}

void test_validate_name(void){
    TEST_ASSERT_EQUAL(1, validate_name("<PERSON> Doe"));
    TEST_ASSERT_EQUAL(1, validate_name("<PERSON><PERSON> <PERSON>"));
    TEST_ASSERT_EQUAL(0, validate_name(""));
    TEST_ASSERT_EQUAL(0, validate_name("J"));
    TEST_ASSERT_EQUAL(0, validate_name("John123"));
}

void test_validate_contact(void){
    TEST_ASSERT_EQUAL(1, validate_contact("**********"));
    TEST_ASSERT_EQUAL(1, validate_contact("************"));
    TEST_ASSERT_EQUAL(0, validate_contact("123"));
    TEST_ASSERT_EQUAL(0, validate_contact("abc1234567"));
}

void test_file_exists(void){
    // Test with a file that should exist
    TEST_ASSERT_EQUAL(1, file_exists("../inc/functions.h"));
    // Test with a file that shouldn't exist
    TEST_ASSERT_EQUAL(0, file_exists("nonexistent_file.txt"));
}

void test_patient_record_validation(void){
    PatientRecord valid_rec = {123, "John Doe", "123 Main St", 30, 'M', "**********", 101};
    PatientRecord invalid_rec = {-1, "", "123 Main St", 200, 'X', "123", -1};

    TEST_ASSERT_EQUAL(1, validate_patient_record(&valid_rec));
    TEST_ASSERT_EQUAL(0, validate_patient_record(&invalid_rec));
}

int main()
{
    /* Initiate the Unity Test Framework */
    UNITY_BEGIN();

    /* Run Test functions */
    RUN_TEST(test_exitt);
    RUN_TEST(test_validate_id);
    RUN_TEST(test_validate_name);
    RUN_TEST(test_validate_contact);
    RUN_TEST(test_file_exists);
    RUN_TEST(test_patient_record_validation);

    return UNITY_END();
}
