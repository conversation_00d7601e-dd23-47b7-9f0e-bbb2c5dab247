
#include "unity.h"
#include "../inc/functions.h"
#include <string.h>

#define PROJECT_NAME "hospital"

/* Required by the unity test framework */
void setUp(){}
/* Required by the unity test framework */
void tearDown(){}

void test_exitt(void){
    // Note: exitt() calls exit(0) so we can't test the return value directly
    // Instead, we'll test that the function exists and can be called
    // In a real scenario, we'd need to mock the exit function
    TEST_ASSERT_NOT_NULL(exitt);
}

void test_validate_id(void){
    TEST_ASSERT_EQUAL(1, validate_id(123));
    TEST_ASSERT_EQUAL(0, validate_id(-1));
    TEST_ASSERT_EQUAL(0, validate_id(0));
    TEST_ASSERT_EQUAL(0, validate_id(1000000));
}

void test_validate_name(void){
    TEST_ASSERT_EQUAL(1, validate_name("<PERSON>"));
    TEST_ASSERT_EQUAL(1, validate_name("Dr. <PERSON>"));
    TEST_ASSERT_EQUAL(0, validate_name(""));
    TEST_ASSERT_EQUAL(0, validate_name("J"));
    TEST_ASSERT_EQUAL(0, validate_name("John123"));
}

void test_validate_contact(void){
    TEST_ASSERT_EQUAL(1, validate_contact("**********"));
    TEST_ASSERT_EQUAL(1, validate_contact("************"));
    TEST_ASSERT_EQUAL(0, validate_contact("123"));
    TEST_ASSERT_EQUAL(0, validate_contact("abc1234567"));
}

void test_file_exists(void){
    // Test with a file that should exist
    TEST_ASSERT_EQUAL(1, file_exists("../inc/functions.h"));
    // Test with a file that shouldn't exist
    TEST_ASSERT_EQUAL(0, file_exists("nonexistent_file.txt"));
}

void test_patient_record_validation(void){
    PatientRecord valid_rec = {123, "John Doe", "123 Main St", 30, 'M', "**********", 101};
    PatientRecord invalid_rec = {-1, "", "123 Main St", 200, 'X', "123", -1};

    TEST_ASSERT_EQUAL(1, validate_patient_record(&valid_rec));
    TEST_ASSERT_EQUAL(0, validate_patient_record(&invalid_rec));
}

int main()
{
    printf("Starting Hospital Management System Tests...\n");

    /* Initiate the Unity Test Framework */
    UNITY_BEGIN();

    /* Run Test functions */
    printf("Running test_exitt...\n");
    RUN_TEST(test_exitt);

    printf("Running test_validate_id...\n");
    RUN_TEST(test_validate_id);

    printf("Running test_validate_name...\n");
    RUN_TEST(test_validate_name);

    printf("Running test_validate_contact...\n");
    RUN_TEST(test_validate_contact);

    printf("Running test_file_exists...\n");
    RUN_TEST(test_file_exists);

    printf("Running test_patient_record_validation...\n");
    RUN_TEST(test_patient_record_validation);

    printf("All tests completed.\n");
    return UNITY_END();
}
