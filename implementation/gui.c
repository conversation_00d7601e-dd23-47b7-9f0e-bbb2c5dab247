#include <gtk/gtk.h>
#include <time.h>
#include <string.h>
#include <stdio.h>
#include <stdlib.h>
#include "inc/functions.h"
#include "inc/dashboard.h"
#include "inc/gui_helpers.h"
#include "gui.h"

// Update the data file path handling to be more robust
#define DATA_FILE "Assignment.dat"

// Helper to trim newline from GTK entry text
void trim_newline(char *str) {
    if (!str) return;
    size_t len = strlen(str);
    if (len > 0 && str[len-1] == '\n') str[len-1] = '\0';
}

// Callback functions for each button
gboolean on_window_delete(GtkWidget *widget, GdkEvent *event, gpointer data) {
    gtk_main_quit();
    return FALSE;
}

void on_about_clicked(GtkMenuItem *menuitem, gpointer user_data) {
    GtkWidget *dialog = gtk_message_dialog_new(NULL, GTK_DIALOG_MODAL, GTK_MESSAGE_INFO, GTK_BUTTONS_OK,
        "Hospital Management System\nVersion 1.0\nDeveloped by Your Team");
    gtk_dialog_run(GTK_DIALOG(dialog));
    gtk_widget_destroy(dialog);
}

void on_delete_clicked(GtkButton *button, gpointer user_data) {
    const char *role = (const char *)user_data;
    if (role && strcmp(role, "admin") != 0) {
        show_error_message("Only admin can delete records!");
        return;
    }

    GtkWidget *dialog = gtk_dialog_new_with_buttons("Delete Record", NULL, GTK_DIALOG_MODAL,
                                         ("_Cancel"), GTK_RESPONSE_CANCEL,
                                         ("_Delete"), GTK_RESPONSE_OK,
                                         NULL);
    GtkWidget *content = gtk_dialog_get_content_area(GTK_DIALOG(dialog));
    GtkWidget *grid = gtk_grid_new();
    gtk_grid_set_row_spacing(GTK_GRID(grid), 5);
    gtk_grid_set_column_spacing(GTK_GRID(grid), 5);
    gtk_container_set_border_width(GTK_CONTAINER(grid), 10);
    GtkWidget *lbl_id = gtk_label_new("Enter Patient ID to Delete:");
    GtkWidget *entry_id = gtk_entry_new();
    gtk_grid_attach(GTK_GRID(grid), lbl_id, 0, 0, 1, 1);
    gtk_grid_attach(GTK_GRID(grid), entry_id, 1, 0, 1, 1);
    gtk_box_pack_start(GTK_BOX(content), grid, TRUE, TRUE, 0);

    gtk_widget_show_all(dialog);
    int response = gtk_dialog_run(GTK_DIALOG(dialog));
    if (response == GTK_RESPONSE_OK) {
        const char *id_text = gtk_entry_get_text(GTK_ENTRY(entry_id));
        trim_newline((char *)id_text);
        int id = atoi(id_text);
        if (delete_patient_record(id)) { // Call backend function
            show_success_message("Record deleted successfully!");
        } else {
            show_error_message("Failed to delete record or record not found!");
        }
    }
    gtk_widget_destroy(dialog);
}

void on_search_clicked(GtkButton *button, gpointer user_data) {
    GtkWidget *dialog = gtk_dialog_new_with_buttons("Search Record", NULL, GTK_DIALOG_MODAL,
                                         ("_Close"), GTK_RESPONSE_CLOSE,
                                         ("_Search"), GTK_RESPONSE_OK,
                                         NULL);
    GtkWidget *content = gtk_dialog_get_content_area(GTK_DIALOG(dialog));
    GtkWidget *grid = gtk_grid_new();
    gtk_grid_set_row_spacing(GTK_GRID(grid), 5);
    gtk_grid_set_column_spacing(GTK_GRID(grid), 5);
    gtk_container_set_border_width(GTK_CONTAINER(grid), 10);
    GtkWidget *lbl_id = gtk_label_new("Enter Patient ID to Search:");
    GtkWidget *entry_id = gtk_entry_new();
    gtk_grid_attach(GTK_GRID(grid), lbl_id, 0, 0, 1, 1);
    gtk_grid_attach(GTK_GRID(grid), entry_id, 1, 0, 1, 1);
    gtk_box_pack_start(GTK_BOX(content), grid, TRUE, TRUE, 0);
    gtk_widget_show_all(dialog);
    if (gtk_dialog_run(GTK_DIALOG(dialog)) == GTK_RESPONSE_OK) {
        int id = atoi(gtk_entry_get_text(GTK_ENTRY(entry_id)));
        PatientRecord rec;
        if (search_patient_record(id, &rec)) {
            char info[256];
            snprintf(info, sizeof(info), "ID: %d\nName: %s\nAddress: %s\nAge: %d\nGender: %c\nContact: %s\nRoom: %d",
                rec.id, rec.full_name, rec.address, rec.age, rec.gender, rec.contact, rec.room);
            GtkWidget *msg = gtk_message_dialog_new(NULL, GTK_DIALOG_MODAL, GTK_MESSAGE_INFO, GTK_BUTTONS_OK, "%s", info);
            gtk_dialog_run(GTK_DIALOG(msg));
            gtk_widget_destroy(msg);
        } else {
            GtkWidget *msg = gtk_message_dialog_new(NULL, GTK_DIALOG_MODAL, GTK_MESSAGE_ERROR, GTK_BUTTONS_OK, "Record not found!");
            gtk_dialog_run(GTK_DIALOG(msg));
            gtk_widget_destroy(msg);
        }
    }
    gtk_widget_destroy(dialog);
}

void on_view_clicked(GtkButton *button, gpointer user_data) {
    GtkWidget *dialog, *scrolled, *textview;
    GtkTextBuffer *buffer;
    dialog = gtk_dialog_new_with_buttons("View Records", NULL, GTK_DIALOG_MODAL,
                                         ("_Close"), GTK_RESPONSE_CLOSE,
                                         NULL);
    GtkWidget *content = gtk_dialog_get_content_area(GTK_DIALOG(dialog));
    scrolled = gtk_scrolled_window_new(NULL, NULL);
    gtk_widget_set_size_request(scrolled, 500, 300);
    gtk_box_pack_start(GTK_BOX(content), scrolled, TRUE, TRUE, 0);
    textview = gtk_text_view_new();
    gtk_text_view_set_editable(GTK_TEXT_VIEW(textview), FALSE);
    gtk_container_add(GTK_CONTAINER(scrolled), textview);
    buffer = gtk_text_view_get_buffer(GTK_TEXT_VIEW(textview));

    // Read and display records
    FILE *fz = fopen(DATA_FILE, "r");
    if (fz) {
        char line[256];
        GtkTextIter iter;
        gtk_text_buffer_get_start_iter(buffer, &iter);
        gtk_text_buffer_insert(buffer, &iter, "ID\tName\tAddress\tAge\tGender\tContact\tRoom\n", -1);
        gtk_text_buffer_insert(buffer, &iter, "-------------------------------------------------------------\n", -1);
        while (fgets(line, sizeof(line), fz)) {
            gtk_text_buffer_insert(buffer, &iter, line, -1);
        }
        fclose(fz);
    } else {
        gtk_text_buffer_set_text(buffer, "No records found or failed to open file.", -1);
    }
    gtk_widget_show_all(dialog);
    gtk_dialog_run(GTK_DIALOG(dialog));
    gtk_widget_destroy(dialog);
}

void on_update_clicked(GtkButton *button, gpointer user_data) {
    GtkWidget *dialog = gtk_dialog_new_with_buttons("Update Record", NULL, GTK_DIALOG_MODAL,
                                         ("_Cancel"), GTK_RESPONSE_CANCEL,
                                         ("_Load"), GTK_RESPONSE_OK,
                                         NULL);
    GtkWidget *content = gtk_dialog_get_content_area(GTK_DIALOG(dialog));
    GtkWidget *grid = gtk_grid_new();
    gtk_grid_set_row_spacing(GTK_GRID(grid), 5);
    gtk_grid_set_column_spacing(GTK_GRID(grid), 5);
    gtk_container_set_border_width(GTK_CONTAINER(grid), 10);
    GtkWidget *lbl_id = gtk_label_new("Enter Patient ID to Update:");
    GtkWidget *entry_id = gtk_entry_new();
    gtk_grid_attach(GTK_GRID(grid), lbl_id, 0, 0, 1, 1);
    gtk_grid_attach(GTK_GRID(grid), entry_id, 1, 0, 1, 1);
    gtk_box_pack_start(GTK_BOX(content), grid, TRUE, TRUE, 0);
    gtk_widget_show_all(dialog);
    if (gtk_dialog_run(GTK_DIALOG(dialog)) == GTK_RESPONSE_OK) {
        int id = atoi(gtk_entry_get_text(GTK_ENTRY(entry_id)));
        PatientRecord rec;
        if (search_patient_record(id, &rec)) {
            GtkWidget *edit_dialog = gtk_dialog_new_with_buttons("Edit Record", NULL, GTK_DIALOG_MODAL,
                                         ("_Cancel"), GTK_RESPONSE_CANCEL,
                                         ("_Save"), GTK_RESPONSE_OK,
                                         NULL);
            GtkWidget *edit_content = gtk_dialog_get_content_area(GTK_DIALOG(edit_dialog));
            GtkWidget *edit_grid = gtk_grid_new();
            gtk_grid_set_row_spacing(GTK_GRID(edit_grid), 5);
            gtk_grid_set_column_spacing(GTK_GRID(edit_grid), 5);
            gtk_container_set_border_width(GTK_CONTAINER(edit_grid), 10);
            GtkWidget *lbl_name = gtk_label_new("Full Name:");
            GtkWidget *lbl_address = gtk_label_new("Address:");
            GtkWidget *lbl_gender = gtk_label_new("Gender (M/F):");
            GtkWidget *lbl_age = gtk_label_new("Age:");
            GtkWidget *lbl_contact = gtk_label_new("Contact:");
            GtkWidget *lbl_room = gtk_label_new("Room No:");
            GtkWidget *entry_name = gtk_entry_new();
            GtkWidget *entry_address = gtk_entry_new();
            GtkWidget *entry_gender = gtk_entry_new();
            GtkWidget *entry_age = gtk_entry_new();
            GtkWidget *entry_contact = gtk_entry_new();
            GtkWidget *entry_room = gtk_entry_new();
            gtk_entry_set_text(GTK_ENTRY(entry_name), rec.full_name);
            gtk_entry_set_text(GTK_ENTRY(entry_address), rec.address);
            char gender_str[2] = {rec.gender, 0};
            gtk_entry_set_text(GTK_ENTRY(entry_gender), gender_str);
            char age_str[8]; snprintf(age_str, sizeof(age_str), "%d", rec.age);
            gtk_entry_set_text(GTK_ENTRY(entry_age), age_str);
            gtk_entry_set_text(GTK_ENTRY(entry_contact), rec.contact);
            char room_str[8]; snprintf(room_str, sizeof(room_str), "%d", rec.room);
            gtk_entry_set_text(GTK_ENTRY(entry_room), room_str);
            gtk_grid_attach(GTK_GRID(edit_grid), lbl_name, 0, 0, 1, 1);
            gtk_grid_attach(GTK_GRID(edit_grid), entry_name, 1, 0, 1, 1);
            gtk_grid_attach(GTK_GRID(edit_grid), lbl_address, 0, 1, 1, 1);
            gtk_grid_attach(GTK_GRID(edit_grid), entry_address, 1, 1, 1, 1);
            gtk_grid_attach(GTK_GRID(edit_grid), lbl_gender, 0, 2, 1, 1);
            gtk_grid_attach(GTK_GRID(edit_grid), entry_gender, 1, 2, 1, 1);
            gtk_grid_attach(GTK_GRID(edit_grid), lbl_age, 0, 3, 1, 1);
            gtk_grid_attach(GTK_GRID(edit_grid), entry_age, 1, 3, 1, 1);
            gtk_grid_attach(GTK_GRID(edit_grid), lbl_contact, 0, 4, 1, 1);
            gtk_grid_attach(GTK_GRID(edit_grid), entry_contact, 1, 4, 1, 1);
            gtk_grid_attach(GTK_GRID(edit_grid), lbl_room, 0, 5, 1, 1);
            gtk_grid_attach(GTK_GRID(edit_grid), entry_room, 1, 5, 1, 1);
            gtk_box_pack_start(GTK_BOX(edit_content), edit_grid, TRUE, TRUE, 0);
            gtk_widget_show_all(edit_dialog);
            if (gtk_dialog_run(GTK_DIALOG(edit_dialog)) == GTK_RESPONSE_OK) {
                strncpy(rec.full_name, gtk_entry_get_text(GTK_ENTRY(entry_name)), sizeof(rec.full_name)-1);
                strncpy(rec.address, gtk_entry_get_text(GTK_ENTRY(entry_address)), sizeof(rec.address)-1);
                rec.gender = gtk_entry_get_text(GTK_ENTRY(entry_gender))[0];
                rec.age = atoi(gtk_entry_get_text(GTK_ENTRY(entry_age)));
                strncpy(rec.contact, gtk_entry_get_text(GTK_ENTRY(entry_contact)), sizeof(rec.contact)-1);
                rec.room = atoi(gtk_entry_get_text(GTK_ENTRY(entry_room)));
                if (update_patient_record(&rec)) {
                    show_success_message("Record updated successfully!");
                } else {
                    show_error_message("Failed to update record!");
                }
            }
            gtk_widget_destroy(edit_dialog);
        } else {
            GtkWidget *msg = gtk_message_dialog_new(NULL, GTK_DIALOG_MODAL, GTK_MESSAGE_ERROR, GTK_BUTTONS_OK, "Record not found!");
            gtk_dialog_run(GTK_DIALOG(msg));
            gtk_widget_destroy(msg);
        }
    }
    gtk_widget_destroy(dialog);
}

void on_room_clicked(GtkButton *button, gpointer user_data) {
    GtkWidget *dialog = gtk_dialog_new_with_buttons("Room Assignment", NULL, GTK_DIALOG_MODAL,
                                         ("_Cancel"), GTK_RESPONSE_CANCEL,
                                         ("_Load"), GTK_RESPONSE_OK,
                                         NULL);
    GtkWidget *content = gtk_dialog_get_content_area(GTK_DIALOG(dialog));
    GtkWidget *grid = gtk_grid_new();
    gtk_grid_set_row_spacing(GTK_GRID(grid), 5);
    gtk_grid_set_column_spacing(GTK_GRID(grid), 5);
    gtk_container_set_border_width(GTK_CONTAINER(grid), 10);
    GtkWidget *lbl_id = gtk_label_new("Enter Patient ID:");
    GtkWidget *entry_id = gtk_entry_new();
    gtk_grid_attach(GTK_GRID(grid), lbl_id, 0, 0, 1, 1);
    gtk_grid_attach(GTK_GRID(grid), entry_id, 1, 0, 1, 1);
    gtk_box_pack_start(GTK_BOX(content), grid, TRUE, TRUE, 0);
    gtk_widget_show_all(dialog);
    if (gtk_dialog_run(GTK_DIALOG(dialog)) == GTK_RESPONSE_OK) {
        int id = atoi(gtk_entry_get_text(GTK_ENTRY(entry_id)));
        PatientRecord rec;
        if (search_patient_record(id, &rec)) {
            GtkWidget *room_dialog = gtk_dialog_new_with_buttons("Edit Room", NULL, GTK_DIALOG_MODAL,
                                         ("_Cancel"), GTK_RESPONSE_CANCEL,
                                         ("_Save"), GTK_RESPONSE_OK,
                                         NULL);
            GtkWidget *room_content = gtk_dialog_get_content_area(GTK_DIALOG(room_dialog));
            GtkWidget *room_grid = gtk_grid_new();
            gtk_grid_set_row_spacing(GTK_GRID(room_grid), 5);
            gtk_grid_set_column_spacing(GTK_GRID(room_grid), 5);
            gtk_container_set_border_width(GTK_CONTAINER(room_grid), 10);
            GtkWidget *lbl_room = gtk_label_new("Room No:");
            GtkWidget *entry_room = gtk_entry_new();
            char room_str[8]; snprintf(room_str, sizeof(room_str), "%d", rec.room);
            gtk_entry_set_text(GTK_ENTRY(entry_room), room_str);
            gtk_grid_attach(GTK_GRID(room_grid), lbl_room, 0, 0, 1, 1);
            gtk_grid_attach(GTK_GRID(room_grid), entry_room, 1, 0, 1, 1);
            gtk_box_pack_start(GTK_BOX(room_content), room_grid, TRUE, TRUE, 0);
            gtk_widget_show_all(room_dialog);
            if (gtk_dialog_run(GTK_DIALOG(room_dialog)) == GTK_RESPONSE_OK) {
                rec.room = atoi(gtk_entry_get_text(GTK_ENTRY(entry_room)));
                if (update_patient_record(&rec)) {
                    show_success_message("Room updated successfully!");
                } else {
                    show_error_message("Failed to update room!");
                }
            }
            gtk_widget_destroy(room_dialog);
        } else {
            GtkWidget *msg = gtk_message_dialog_new(NULL, GTK_DIALOG_MODAL, GTK_MESSAGE_ERROR, GTK_BUTTONS_OK, "Record not found!");
            gtk_dialog_run(GTK_DIALOG(msg));
            gtk_widget_destroy(msg);
        }
    }
    gtk_widget_destroy(dialog);
}

void on_exit_clicked(GtkButton *button, gpointer user_data) {
    gtk_main_quit();
}

// Add Record dialog and logic

void on_add_clicked(GtkButton *button, gpointer user_data) {
    GtkWidget *dialog, *content, *grid;
    GtkWidget *entry_id, *entry_name, *entry_address, *entry_gender, *entry_age, *entry_contact, *entry_room;
    dialog = gtk_dialog_new_with_buttons("Add Record", NULL, GTK_DIALOG_MODAL,
                                         ("_Cancel"), GTK_RESPONSE_CANCEL,
                                         ("_Add"), GTK_RESPONSE_OK,
                                         NULL);
    content = gtk_dialog_get_content_area(GTK_DIALOG(dialog));
    grid = gtk_grid_new();
    gtk_grid_set_row_spacing(GTK_GRID(grid), 5);
    gtk_grid_set_column_spacing(GTK_GRID(grid), 5);
    gtk_container_set_border_width(GTK_CONTAINER(grid), 10);

    GtkWidget *lbl_id = gtk_label_new("ID:");
    GtkWidget *lbl_name = gtk_label_new("Full Name:");
    GtkWidget *lbl_address = gtk_label_new("Address:");
    GtkWidget *lbl_gender = gtk_label_new("Gender (M/F):");
    GtkWidget *lbl_age = gtk_label_new("Age:");
    GtkWidget *lbl_contact = gtk_label_new("Contact:");
    GtkWidget *lbl_room = gtk_label_new("Room No:");
    entry_id = gtk_entry_new();
    entry_name = gtk_entry_new();
    entry_address = gtk_entry_new();
    entry_gender = gtk_entry_new();
    entry_age = gtk_entry_new();
    entry_contact = gtk_entry_new();
    entry_room = gtk_entry_new();

    gtk_grid_attach(GTK_GRID(grid), lbl_id, 0, 0, 1, 1);
    gtk_grid_attach(GTK_GRID(grid), entry_id, 1, 0, 1, 1);
    gtk_grid_attach(GTK_GRID(grid), lbl_name, 0, 1, 1, 1);
    gtk_grid_attach(GTK_GRID(grid), entry_name, 1, 1, 1, 1);
    gtk_grid_attach(GTK_GRID(grid), lbl_address, 0, 2, 1, 1);
    gtk_grid_attach(GTK_GRID(grid), entry_address, 1, 2, 1, 1);
    gtk_grid_attach(GTK_GRID(grid), lbl_gender, 0, 3, 1, 1);
    gtk_grid_attach(GTK_GRID(grid), entry_gender, 1, 3, 1, 1);
    gtk_grid_attach(GTK_GRID(grid), lbl_age, 0, 4, 1, 1);
    gtk_grid_attach(GTK_GRID(grid), entry_age, 1, 4, 1, 1);
    gtk_grid_attach(GTK_GRID(grid), lbl_contact, 0, 5, 1, 1);
    gtk_grid_attach(GTK_GRID(grid), entry_contact, 1, 5, 1, 1);
    gtk_grid_attach(GTK_GRID(grid), lbl_room, 0, 6, 1, 1);
    gtk_grid_attach(GTK_GRID(grid), entry_room, 1, 6, 1, 1);

    gtk_box_pack_start(GTK_BOX(content), grid, TRUE, TRUE, 0);
    gtk_widget_show_all(dialog);
    if (gtk_dialog_run(GTK_DIALOG(dialog)) == GTK_RESPONSE_OK) {
        PatientRecord rec;
        rec.id = atoi(gtk_entry_get_text(GTK_ENTRY(entry_id)));
        strncpy(rec.full_name, gtk_entry_get_text(GTK_ENTRY(entry_name)), sizeof(rec.full_name)-1);
        strncpy(rec.address, gtk_entry_get_text(GTK_ENTRY(entry_address)), sizeof(rec.address)-1);
        rec.gender = gtk_entry_get_text(GTK_ENTRY(entry_gender))[0];
        rec.age = atoi(gtk_entry_get_text(GTK_ENTRY(entry_age)));
        strncpy(rec.contact, gtk_entry_get_text(GTK_ENTRY(entry_contact)), sizeof(rec.contact)-1);
        rec.room = atoi(gtk_entry_get_text(GTK_ENTRY(entry_room)));
        if (add_patient_record(&rec)) {
            show_success_message("Record added successfully!");
        } else {
            show_error_message("Failed to add record!");
        }
    }
    gtk_widget_destroy(dialog);
}

void on_export_csv_clicked(GtkButton *button, gpointer user_data) {
    FILE *fz = fopen(DATA_FILE, "r");
    FILE *csv = fopen("patients.csv", "w");
    if (!fz || !csv) {
        show_error_message("Failed to open data file or create CSV file!");
        if (fz) fclose(fz);
        if (csv) fclose(csv);
        return;
    }
    fprintf(csv, "ID,Name,Address,Age,Gender,Contact,Room\n");
    char id[32], name[64], address[64], age[16], gender[16], contact[32], room[16];
    while (fscanf(fz, "%31s %63s %63s %15s %15s %31s %15s", id, name, address, age, gender, contact, room) == 7) {
        fprintf(csv, "%s,%s,%s,%s,%s,%s,%s\n", id, name, address, age, gender, contact, room);
    }
    fclose(fz);
    fclose(csv);
    show_success_message("Exported to patients.csv successfully!");
}

// User authentication dialog
int show_login_dialog(char *role_out) {
    GtkWidget *dialog, *content, *grid;
    GtkWidget *entry_user, *entry_pass;
    int result = 0;
    dialog = gtk_dialog_new_with_buttons("Login", NULL, GTK_DIALOG_MODAL,
                                         ("_Cancel"), GTK_RESPONSE_CANCEL,
                                         ("_Login"), GTK_RESPONSE_OK,
                                         NULL);
    content = gtk_dialog_get_content_area(GTK_DIALOG(dialog));
    grid = gtk_grid_new();
    gtk_grid_set_row_spacing(GTK_GRID(grid), 5);
    gtk_grid_set_column_spacing(GTK_GRID(grid), 5);
    gtk_container_set_border_width(GTK_CONTAINER(grid), 10);

    GtkWidget *lbl_user = gtk_label_new("Username:");
    GtkWidget *lbl_pass = gtk_label_new("Password:");
    entry_user = gtk_entry_new();
    entry_pass = gtk_entry_new();
    gtk_entry_set_visibility(GTK_ENTRY(entry_pass), FALSE);
    gtk_entry_set_invisible_char(GTK_ENTRY(entry_pass), '*');

    gtk_grid_attach(GTK_GRID(grid), lbl_user, 0, 0, 1, 1);
    gtk_grid_attach(GTK_GRID(grid), entry_user, 1, 0, 1, 1);
    gtk_grid_attach(GTK_GRID(grid), lbl_pass, 0, 1, 1, 1);
    gtk_grid_attach(GTK_GRID(grid), entry_pass, 1, 1, 1, 1);
    gtk_box_pack_start(GTK_BOX(content), grid, TRUE, TRUE, 0);
    gtk_widget_show_all(dialog);

    while (1) {
        int resp = gtk_dialog_run(GTK_DIALOG(dialog));
        if (resp != GTK_RESPONSE_OK) {
            result = 0;
            break;
        }
        const char *user = gtk_entry_get_text(GTK_ENTRY(entry_user));
        const char *pass = gtk_entry_get_text(GTK_ENTRY(entry_pass));
        // Hardcoded credentials: admin/admin123, staff/staff123
        if (strcmp(user, "admin") == 0 && strcmp(pass, "admin123") == 0) {
            strcpy(role_out, "admin");
            result = 1;
            break;
        } else if (strcmp(user, "staff") == 0 && strcmp(pass, "staff123") == 0) {
            strcpy(role_out, "staff");
            result = 1;
            break;
        } else {
            GtkWidget *err = gtk_message_dialog_new(NULL, GTK_DIALOG_MODAL, GTK_MESSAGE_ERROR, GTK_BUTTONS_OK, "Invalid username or password!");
            gtk_dialog_run(GTK_DIALOG(err));
            gtk_widget_destroy(err);
        }
    }
    gtk_widget_destroy(dialog);
    return result;
}

void on_payment_clicked(GtkButton *button, gpointer user_data) {
    GtkWidget *dialog, *content, *grid;
    GtkWidget *entry_id, *entry_amount, *entry_method, *entry_status;
    dialog = gtk_dialog_new_with_buttons("Payment Details", NULL, GTK_DIALOG_MODAL,
                                         ("_Cancel"), GTK_RESPONSE_CANCEL,
                                         ("_Save"), GTK_RESPONSE_OK,
                                         NULL);
    content = gtk_dialog_get_content_area(GTK_DIALOG(dialog));
    grid = gtk_grid_new();
    gtk_grid_set_row_spacing(GTK_GRID(grid), 5);
    gtk_grid_set_column_spacing(GTK_GRID(grid), 5);
    gtk_container_set_border_width(GTK_CONTAINER(grid), 10);

    GtkWidget *lbl_id = gtk_label_new("Patient ID:");
    GtkWidget *lbl_amount = gtk_label_new("Amount:");
    GtkWidget *lbl_method = gtk_label_new("Payment Method:");
    GtkWidget *lbl_status = gtk_label_new("Status (Paid/Unpaid):");
    entry_id = gtk_entry_new();
    entry_amount = gtk_entry_new();
    entry_method = gtk_entry_new();
    entry_status = gtk_entry_new();

    gtk_grid_attach(GTK_GRID(grid), lbl_id, 0, 0, 1, 1);
    gtk_grid_attach(GTK_GRID(grid), entry_id, 1, 0, 1, 1);
    gtk_grid_attach(GTK_GRID(grid), lbl_amount, 0, 1, 1, 1);
    gtk_grid_attach(GTK_GRID(grid), entry_amount, 1, 1, 1, 1);
    gtk_grid_attach(GTK_GRID(grid), lbl_method, 0, 2, 1, 1);
    gtk_grid_attach(GTK_GRID(grid), entry_method, 1, 2, 1, 1);
    gtk_grid_attach(GTK_GRID(grid), lbl_status, 0, 3, 1, 1);
    gtk_grid_attach(GTK_GRID(grid), entry_status, 1, 3, 1, 1);

    gtk_box_pack_start(GTK_BOX(content), grid, TRUE, TRUE, 0);
    gtk_widget_show_all(dialog);

    if (gtk_dialog_run(GTK_DIALOG(dialog)) == GTK_RESPONSE_OK) {
        const char *id = gtk_entry_get_text(GTK_ENTRY(entry_id));
        const char *amount = gtk_entry_get_text(GTK_ENTRY(entry_amount));
        const char *method = gtk_entry_get_text(GTK_ENTRY(entry_method));
        const char *status = gtk_entry_get_text(GTK_ENTRY(entry_status));
        if (strlen(id) == 0 || strlen(amount) == 0 || strlen(method) == 0 || strlen(status) == 0) {
            GtkWidget *err = gtk_message_dialog_new(NULL, GTK_DIALOG_MODAL, GTK_MESSAGE_ERROR, GTK_BUTTONS_OK, "All fields are required!");
            gtk_dialog_run(GTK_DIALOG(err));
            gtk_widget_destroy(err);
        } else {
            FILE *pay = fopen("payments.csv", "a");
            if (pay) {
                fprintf(pay, "%s,%s,%s,%s\n", id, amount, method, status);
                fclose(pay);
                GtkWidget *ok = gtk_message_dialog_new(NULL, GTK_DIALOG_MODAL, GTK_MESSAGE_INFO, GTK_BUTTONS_OK, "Payment details saved!");
                gtk_dialog_run(GTK_DIALOG(ok));
                gtk_widget_destroy(ok);
            } else {
                GtkWidget *err = gtk_message_dialog_new(NULL, GTK_DIALOG_MODAL, GTK_MESSAGE_ERROR, GTK_BUTTONS_OK, "Failed to save payment details!");
                gtk_dialog_run(GTK_DIALOG(err));
                gtk_widget_destroy(err);
            }
        }
    }
    gtk_widget_destroy(dialog);
}

// Helper to get patient name by ID from Assignment.dat
void get_patient_name_by_id(const char *patient_id, char *name_out, size_t name_size) {
    FILE *fz = fopen(DATA_FILE, "r");
    if (!fz) {
        strncpy(name_out, "Unknown", name_size);
        return;
    }
    char line[256];
    while (fgets(line, sizeof(line), fz)) {
        char id[32], name[128];
        // Assuming ID is first, Name is second, tab-separated
        if (sscanf(line, "%31[^\t]\t%127[^\t]", id, name) == 2) {
            if (strcmp(id, patient_id) == 0) {
                strncpy(name_out, name, name_size);
                fclose(fz);
                return;
            }
        }
    }
    fclose(fz);
    strncpy(name_out, "Unknown", name_size);
}

// Enhanced View Payments dialog with search and receipt export
void on_view_payments_clicked(GtkButton *button, gpointer user_data) {
    GtkWidget *dialog, *scrolled, *textview, *search_entry, *hbox, *btn_receipt;
    GtkTextBuffer *buffer;
    dialog = gtk_dialog_new_with_buttons("View Payments", NULL, GTK_DIALOG_MODAL,
                                         ("_Close"), GTK_RESPONSE_CLOSE,
                                         NULL);
    GtkWidget *content = gtk_dialog_get_content_area(GTK_DIALOG(dialog));
    hbox = gtk_box_new(GTK_ORIENTATION_HORIZONTAL, 5);
    search_entry = gtk_entry_new();
    gtk_entry_set_placeholder_text(GTK_ENTRY(search_entry), "Search by Patient ID");
    btn_receipt = gtk_button_new_with_label("Export Receipt");
    gtk_box_pack_start(GTK_BOX(hbox), search_entry, TRUE, TRUE, 0);
    gtk_box_pack_start(GTK_BOX(hbox), btn_receipt, FALSE, FALSE, 0);
    gtk_box_pack_start(GTK_BOX(content), hbox, FALSE, FALSE, 5);
    scrolled = gtk_scrolled_window_new(NULL, NULL);
    gtk_widget_set_size_request(scrolled, 500, 300);
    gtk_box_pack_start(GTK_BOX(content), scrolled, TRUE, TRUE, 0);
    textview = gtk_text_view_new();
    gtk_text_view_set_editable(GTK_TEXT_VIEW(textview), FALSE);
    gtk_container_add(GTK_CONTAINER(scrolled), textview);
    buffer = gtk_text_view_get_buffer(GTK_TEXT_VIEW(textview));

    // Helper to refresh payment list
    void refresh_payments(const char *filter_id) {
        FILE *pay = fopen("payments.csv", "r");
        GtkTextIter iter;
        gtk_text_buffer_set_text(buffer, "", -1);
        gtk_text_buffer_get_start_iter(buffer, &iter);
        gtk_text_buffer_insert(buffer, &iter, "PatientID\tName\tAmount\tMethod\tStatus\n", -1);
        gtk_text_buffer_insert(buffer, &iter, "-------------------------------------------------------------\n", -1);
        if (pay) {
            char line[256];
            while (fgets(line, sizeof(line), pay)) {
                char pid[32], amount[32], method[32], status[32];
                if (sscanf(line, "%31[^,],%31[^,],%31[^,],%31[^\n]", pid, amount, method, status) == 4) {
                    if (!filter_id || strlen(filter_id) == 0 || strstr(pid, filter_id)) {
                        char pname[128];
                        get_patient_name_by_id(pid, pname, sizeof(pname));
                        char out[512];
                        snprintf(out, sizeof(out), "%s\t%s\t%s\t%s\t%s\n", pid, pname, amount, method, status);
                        gtk_text_buffer_insert(buffer, &iter, out, -1);
                    }
                }
            }
            fclose(pay);
        }
    }

    // Initial load
    refresh_payments("");

    // Search callback
    void on_search_changed(GtkEntry *entry, gpointer data) {
        const char *filter = gtk_entry_get_text(entry);
        refresh_payments(filter);
    }
    g_signal_connect(search_entry, "changed", G_CALLBACK(on_search_changed), NULL);

    // Receipt export callback
    void on_receipt_clicked(GtkButton *btn, gpointer data) {
        GtkClipboard *clipboard = gtk_clipboard_get(GDK_SELECTION_CLIPBOARD);
        GtkTextIter start, end;
        gtk_text_buffer_get_start_iter(buffer, &start);
        gtk_text_buffer_get_end_iter(buffer, &end);
        gchar *text = gtk_text_buffer_get_text(buffer, &start, &end, FALSE);
        gtk_clipboard_set_text(clipboard, text, -1);
        GtkWidget *msg = gtk_message_dialog_new(NULL, GTK_DIALOG_MODAL, GTK_MESSAGE_INFO, GTK_BUTTONS_OK, "Receipt copied to clipboard! Paste into a document to print/export.");
        gtk_dialog_run(GTK_DIALOG(msg));
        gtk_widget_destroy(msg);
        g_free(text);
    }
    g_signal_connect(btn_receipt, "clicked", G_CALLBACK(on_receipt_clicked), NULL);

    gtk_widget_show_all(dialog);
    gtk_dialog_run(GTK_DIALOG(dialog));
    gtk_widget_destroy(dialog);
}

// Dashboard window callback
void on_dashboard_clicked(GtkButton *button, gpointer user_data) {
    GtkWidget *dialog = gtk_dialog_new_with_buttons("Hospital Dashboard", NULL, GTK_DIALOG_MODAL,
                                         ("_Close"), GTK_RESPONSE_CLOSE,
                                         NULL);
    GtkWidget *content = gtk_dialog_get_content_area(GTK_DIALOG(dialog));
    GtkWidget *grid = gtk_grid_new();
    gtk_grid_set_row_spacing(GTK_GRID(grid), 10);
    gtk_grid_set_column_spacing(GTK_GRID(grid), 10);
    gtk_container_set_border_width(GTK_CONTAINER(grid), 10);

    // Example statistics
    int total_patients = count_records("patients.csv");
    int total_doctors = count_records("doctors.dat");
    double total_revenue = calculate_total_revenue("payments.csv");

    GtkWidget *lbl_patients = gtk_label_new("Total Patients:");
    GtkWidget *lbl_doctors = gtk_label_new("Total Doctors:");
    GtkWidget *lbl_revenue = gtk_label_new("Total Revenue:");

    char patients_str[16], doctors_str[16], revenue_str[32];
    snprintf(patients_str, sizeof(patients_str), "%d", total_patients);
    snprintf(doctors_str, sizeof(doctors_str), "%d", total_doctors);
    snprintf(revenue_str, sizeof(revenue_str), "%.2f", total_revenue);

    GtkWidget *val_patients = gtk_label_new(patients_str);
    GtkWidget *val_doctors = gtk_label_new(doctors_str);
    GtkWidget *val_revenue = gtk_label_new(revenue_str);

    gtk_grid_attach(GTK_GRID(grid), lbl_patients, 0, 0, 1, 1);
    gtk_grid_attach(GTK_GRID(grid), val_patients, 1, 0, 1, 1);
    gtk_grid_attach(GTK_GRID(grid), lbl_doctors, 0, 1, 1, 1);
    gtk_grid_attach(GTK_GRID(grid), val_doctors, 1, 1, 1, 1);
    gtk_grid_attach(GTK_GRID(grid), lbl_revenue, 0, 2, 1, 1);
    gtk_grid_attach(GTK_GRID(grid), val_revenue, 1, 2, 1, 1);

    gtk_box_pack_start(GTK_BOX(content), grid, TRUE, TRUE, 0);
    gtk_widget_show_all(dialog);
    gtk_dialog_run(GTK_DIALOG(dialog));
    gtk_widget_destroy(dialog);
}

void on_upload_data_clicked(GtkButton *button, gpointer user_data) {
    GtkWidget *dialog = gtk_file_chooser_dialog_new("Upload Data File", NULL, GTK_FILE_CHOOSER_ACTION_OPEN,
                                         ("_Cancel"), GTK_RESPONSE_CANCEL,
                                         ("_Open"), GTK_RESPONSE_ACCEPT,
                                         NULL);

    if (gtk_dialog_run(GTK_DIALOG(dialog)) == GTK_RESPONSE_ACCEPT) {
        char *filename = gtk_file_chooser_get_filename(GTK_FILE_CHOOSER(dialog));
        if (process_uploaded_file(filename)) {
            show_success_message("Data uploaded successfully!");
        } else {
            show_error_message("Failed to upload data. Please check the file format.");
        }
        g_free(filename);
    }
    gtk_widget_destroy(dialog);
}

void on_logout_clicked(GtkMenuItem *item, gpointer user_data) {
    gtk_widget_hide(main_window);

    // Clear login fields
    GtkEntry *username_entry = GTK_ENTRY(gtk_builder_get_object(builder, "username_entry"));
    GtkEntry *password_entry = GTK_ENTRY(gtk_builder_get_object(builder, "password_entry"));
    gtk_entry_set_text(username_entry, "");
    gtk_entry_set_text(password_entry, "");

    // Show login window again
    gtk_widget_show_all(login_window);
}

GtkWidget *login_window = NULL;
GtkWidget *main_window = NULL;
GtkBuilder *builder = NULL;
char current_user_role[10] = "";

int gui_main(int argc, char *argv[]) {
    gtk_init(&argc, &argv);

    // Load the Glade file
    GError *error = NULL;
    builder = gtk_builder_new();
    if (!gtk_builder_add_from_file(builder, "gui.glade", &error)) {
        g_printerr("Error loading GUI file: %s\n", error->message);
        g_error_free(error);
        return 1;
    }

    // Get the login window
    login_window = GTK_WIDGET(gtk_builder_get_object(builder, "login_window"));
    if (!login_window) {
        g_printerr("Could not get login window\n");
        return 1;
    }

    // Connect login signals
    GtkWidget *login_button = GTK_WIDGET(gtk_builder_get_object(builder, "login_button"));
    g_signal_connect(login_button, "clicked", G_CALLBACK(on_login_clicked), NULL);
    g_signal_connect(login_window, "destroy", G_CALLBACK(gtk_main_quit), NULL);

    // Show login window
    gtk_widget_show_all(login_window);
    gtk_main();
    return 0;
}

// Authentication handling
void on_login_clicked(GtkButton *button, gpointer user_data) {
    GtkEntry *username_entry = GTK_ENTRY(gtk_builder_get_object(builder, "username_entry"));
    GtkEntry *password_entry = GTK_ENTRY(gtk_builder_get_object(builder, "password_entry"));

    const char *username = gtk_entry_get_text(username_entry);
    const char *password = gtk_entry_get_text(password_entry);

    // Simple authentication check
    if (strcmp(username, "admin") == 0 && strcmp(password, "admin123") == 0) {
        strcpy(current_user_role, "admin");
        show_main_window("admin");
    } else if (strcmp(username, "staff") == 0 && strcmp(password, "staff123") == 0) {
        strcpy(current_user_role, "staff");
        show_main_window("staff");
    } else {
        show_error_message("Invalid username or password!");
        return;
    }

    // Hide login window
    gtk_widget_hide(login_window);
}

void show_main_window(const char *role) {
    // Get main window
    main_window = GTK_WIDGET(gtk_builder_get_object(builder, "main_window"));
    if (!main_window) {
        show_error_message("Could not load main window!");
        return;
    }

    // Connect menu signals
    GtkWidget *menu_logout = GTK_WIDGET(gtk_builder_get_object(builder, "menu_logout"));
    GtkWidget *menu_exit = GTK_WIDGET(gtk_builder_get_object(builder, "menu_exit"));
    g_signal_connect(menu_logout, "activate", G_CALLBACK(on_logout_clicked), NULL);
    g_signal_connect(menu_exit, "activate", G_CALLBACK(gtk_main_quit), NULL);

    // Connect patient management signals
    g_signal_connect(gtk_builder_get_object(builder, "btn_add_patient"), "clicked",
                    G_CALLBACK(on_add_clicked), NULL);
    g_signal_connect(gtk_builder_get_object(builder, "btn_view_patients"), "clicked",
                    G_CALLBACK(on_view_clicked), NULL);
    g_signal_connect(gtk_builder_get_object(builder, "btn_search_patient"), "clicked",
                    G_CALLBACK(on_search_clicked), NULL);
    g_signal_connect(gtk_builder_get_object(builder, "btn_update_patient"), "clicked",
                    G_CALLBACK(on_update_clicked), NULL);
    g_signal_connect(gtk_builder_get_object(builder, "btn_delete_patient"), "clicked",
                    G_CALLBACK(on_delete_clicked), NULL);
    g_signal_connect(gtk_builder_get_object(builder, "btn_room"), "clicked",
                    G_CALLBACK(on_room_clicked), NULL);

    // Connect doctor management signals
    g_signal_connect(gtk_builder_get_object(builder, "btn_add_doctor"), "clicked",
                    G_CALLBACK(on_add_doctor_clicked), NULL);
    g_signal_connect(gtk_builder_get_object(builder, "btn_view_doctors"), "clicked",
                    G_CALLBACK(on_view_doctor_clicked), NULL);
    g_signal_connect(gtk_builder_get_object(builder, "btn_search_doctor"), "clicked",
                    G_CALLBACK(on_search_doctor_clicked), NULL);
    g_signal_connect(gtk_builder_get_object(builder, "btn_update_doctor"), "clicked",
                    G_CALLBACK(on_update_doctor_clicked), NULL);
    g_signal_connect(gtk_builder_get_object(builder, "btn_delete_doctor"), "clicked",
                    G_CALLBACK(on_delete_doctor_clicked), NULL);

    // Initialize visualization area
    GtkWidget *visualization_area = GTK_WIDGET(gtk_builder_get_object(builder, "visualization_area"));
    if (visualization_area) {
        DashboardStats stats;
        get_dashboard_stats(&stats);
        GtkWidget *new_visualization_area = create_visualization_area(&stats);
        gtk_widget_destroy(visualization_area); // Remove placeholder
        gtk_box_pack_start(GTK_BOX(gtk_widget_get_parent(new_visualization_area)), new_visualization_area, TRUE, TRUE, 0);
        gtk_widget_show(new_visualization_area);
    }

    // If not admin, disable certain features
    if (strcmp(role, "admin") != 0) {
        gtk_widget_set_sensitive(GTK_WIDGET(gtk_builder_get_object(builder, "btn_delete_patient")), FALSE);
        gtk_widget_set_sensitive(GTK_WIDGET(gtk_builder_get_object(builder, "btn_delete_doctor")), FALSE);
        gtk_widget_set_sensitive(GTK_WIDGET(gtk_builder_get_object(builder, "btn_delete_staff")), FALSE);
        gtk_widget_set_sensitive(GTK_WIDGET(gtk_builder_get_object(builder, "btn_delete_ward")), FALSE);
    }

    gtk_widget_show_all(main_window);
}

void on_add_doctor_clicked(GtkButton *button, gpointer user_data) {
    // Placeholder implementation for adding a doctor
    show_success_message("Add doctor functionality invoked.");
}

void on_view_doctor_clicked(GtkButton *button, gpointer user_data) {
    // Placeholder implementation for viewing doctors
    show_success_message("View doctors functionality invoked.");
}

void on_search_doctor_clicked(GtkButton *button, gpointer user_data) {
    // Placeholder implementation for searching a doctor
    show_success_message("Search doctor functionality invoked.");
}

void on_update_doctor_clicked(GtkButton *button, gpointer user_data) {
    // Placeholder implementation for updating a doctor
    show_success_message("Update doctor functionality invoked.");
}

void on_delete_doctor_clicked(GtkButton *button, gpointer user_data) {
    // Placeholder implementation for deleting a doctor
    show_success_message("Delete doctor functionality invoked.");
}
