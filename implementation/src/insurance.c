#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include "../inc/functions.h"

// Insurance Management Implementation
int add_insurance_record(const InsuranceRecord *insurance) {
    if (!insurance || !validate_id(insurance->patient_id)) {
        return -1;
    }
    
    FILE *fp = fopen(INSURANCE_FILE, "ab");
    if (!fp) return -1;
    
    int success = fwrite(insurance, sizeof(InsuranceRecord), 1, fp) == 1;
    fclose(fp);
    return success ? 0 : -1;
}

int get_patient_insurance(int patient_id, InsuranceRecord *insurance) {
    if (!insurance || !validate_id(patient_id)) return -1;
    
    FILE *fp = fopen(INSURANCE_FILE, "rb");
    if (!fp) return -1;

    int found = 0;
    while (fread(insurance, sizeof(InsuranceRecord), 1, fp)) {
        if (insurance->patient_id == patient_id) {
            found = 1;
            break;
        }
    }
    fclose(fp);
    return found ? 0 : -1;
}

int update_insurance_record(const InsuranceRecord *insurance) {
    if (!insurance || !validate_id(insurance->patient_id)) return -1;
    
    FILE *fp = fopen(INSURANCE_FILE, "r+b");
    if (!fp) return -1;

    InsuranceRecord temp;
    int found = 0;
    while (fread(&temp, sizeof(InsuranceRecord), 1, fp)) {
        if (temp.patient_id == insurance->patient_id) {
            fseek(fp, -sizeof(InsuranceRecord), SEEK_CUR);
            found = fwrite(insurance, sizeof(InsuranceRecord), 1, fp) == 1;
            break;
        }
    }
    fclose(fp);
    return found ? 0 : -1;
}

int delete_insurance_record(int patient_id) {
    if (!validate_id(patient_id)) return -1;
    
    FILE *fp = fopen(INSURANCE_FILE, "rb");
    FILE *temp = fopen("temp_insurance.dat", "wb");
    if (!fp || !temp) {
        if (fp) fclose(fp);
        if (temp) fclose(temp);
        return -1;
    }

    InsuranceRecord rec;
    int found = 0;
    while (fread(&rec, sizeof(InsuranceRecord), 1, fp)) {
        if (rec.patient_id != patient_id) {
            fwrite(&rec, sizeof(InsuranceRecord), 1, temp);
        } else {
            found = 1;
        }
    }

    fclose(fp);
    fclose(temp);
    remove(INSURANCE_FILE);
    rename("temp_insurance.dat", INSURANCE_FILE);
    return found ? 0 : -1;
}

void list_insurance_records(void) {
    FILE *fp = fopen(INSURANCE_FILE, "rb");
    if (!fp) {
        printf("No insurance records found.\n");
        return;
    }

    InsuranceRecord rec;
    printf("\nInsurance Records:\n");
    printf("%-10s | %-20s | %-15s | %-12s | %-12s\n", 
           "Patient ID", "Provider", "Policy Number", "Coverage", "Expiry Date");
    printf("------------------------------------------------------------------------\n");
    
    int count = 0;
    while (fread(&rec, sizeof(InsuranceRecord), 1, fp)) {
        printf("%-10d | %-20s | %-15s | $%-11.2f | %-12s\n",
               rec.patient_id, rec.provider, rec.policy_number, 
               rec.coverage_amount, rec.expiry_date);
        count++;
    }
    
    fclose(fp);
    printf("\nTotal insurance records: %d\n", count);
}

// Check if insurance is valid (not expired)
int is_insurance_valid(const InsuranceRecord *insurance) {
    if (!insurance) return 0;
    
    time_t t = time(NULL);
    struct tm tm = *localtime(&t);
    char today[11];
    sprintf(today, "%04d-%02d-%02d", tm.tm_year + 1900, tm.tm_mon + 1, tm.tm_mday);
    
    return strcmp(insurance->expiry_date, today) > 0;
}

// Get total insurance coverage for all patients
double get_total_insurance_coverage(void) {
    FILE *fp = fopen(INSURANCE_FILE, "rb");
    if (!fp) return 0.0;

    double total = 0.0;
    InsuranceRecord rec;
    while (fread(&rec, sizeof(InsuranceRecord), 1, fp)) {
        if (is_insurance_valid(&rec)) {
            total += rec.coverage_amount;
        }
    }
    fclose(fp);
    return total;
}

// Get insurance coverage for a specific patient
double get_patient_coverage(int patient_id) {
    InsuranceRecord insurance;
    if (get_patient_insurance(patient_id, &insurance) == 0 && is_insurance_valid(&insurance)) {
        return insurance.coverage_amount;
    }
    return 0.0;
}

// Validate insurance record
int validate_insurance_record(const InsuranceRecord *insurance) {
    if (!insurance) return 0;
    if (!validate_id(insurance->patient_id)) return 0;
    if (strlen(insurance->provider) < 2) return 0;
    if (strlen(insurance->policy_number) < 5) return 0;
    if (insurance->coverage_amount <= 0) return 0;
    if (strlen(insurance->expiry_date) != 10) return 0;
    return 1;
}

// Get expiring insurance policies (within 30 days)
int get_expiring_policies(InsuranceRecord *policies, int max_count) {
    if (!policies || max_count <= 0) return 0;
    
    FILE *fp = fopen(INSURANCE_FILE, "rb");
    if (!fp) return 0;

    time_t t = time(NULL);
    struct tm tm = *localtime(&t);
    tm.tm_mday += 30; // 30 days from now
    mktime(&tm); // Normalize the date
    
    char future_date[11];
    sprintf(future_date, "%04d-%02d-%02d", tm.tm_year + 1900, tm.tm_mon + 1, tm.tm_mday);

    InsuranceRecord rec;
    int count = 0;
    while (fread(&rec, sizeof(InsuranceRecord), 1, fp) && count < max_count) {
        if (strcmp(rec.expiry_date, future_date) <= 0) {
            policies[count++] = rec;
        }
    }
    
    fclose(fp);
    return count;
}

// Calculate insurance claim amount for a bill
double calculate_insurance_claim(int patient_id, double bill_amount) {
    InsuranceRecord insurance;
    if (get_patient_insurance(patient_id, &insurance) != 0 || !is_insurance_valid(&insurance)) {
        return 0.0;
    }
    
    // Simple calculation: cover up to 80% of bill or coverage amount, whichever is lower
    double claim_amount = bill_amount * 0.8;
    if (claim_amount > insurance.coverage_amount) {
        claim_amount = insurance.coverage_amount;
    }
    
    return claim_amount;
}
