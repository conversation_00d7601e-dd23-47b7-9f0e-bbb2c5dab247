#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include "../inc/functions.h"

#define MEDICAL_RECORDS_FILE "medical_records.dat"

// Structure for medical records
typedef struct {
    int id;
    int patient_id;
    int doctor_id;
    char date[20];
    char diagnosis[200];
    char prescription[500];
    char test_results[300];
    char notes[500];
    float charges;
} MedicalRecord;

int add_medical_record(const MedicalRecord *rec) {
    FILE *fp = fopen(MEDICAL_RECORDS_FILE, "ab");
    if (!fp) return 0;
    int success = fwrite(rec, sizeof(MedicalRecord), 1, fp) == 1;
    fclose(fp);
    return success;
}

int get_patient_medical_history(int patient_id, MedicalRecord *records, int max_count) {
    FILE *fp = fopen(MEDICAL_RECORDS_FILE, "rb");
    if (!fp) return 0;

    int count = 0;
    MedicalRecord rec;
    while (fread(&rec, sizeof(MedicalRecord), 1, fp) && count < max_count) {
        if (rec.patient_id == patient_id) {
            records[count++] = rec;
        }
    }
    fclose(fp);
    return count;
}

// Generate detailed medical report in HTML format
int generate_medical_report(int patient_id, const char *output_file) {
    FILE *fp = fopen(output_file, "w");
    if (!fp) return 0;

    // Write HTML header
    fprintf(fp, "<!DOCTYPE html>\n<html>\n<head>\n");
    fprintf(fp, "<style>\n");
    fprintf(fp, "body { font-family: Arial, sans-serif; margin: 40px; }\n");
    fprintf(fp, "table { border-collapse: collapse; width: 100%%; }\n");
    fprintf(fp, "th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\n");
    fprintf(fp, "th { background-color: #f2f2f2; }\n");
    fprintf(fp, "</style>\n</head>\n<body>\n");

    // Get patient details
    PatientRecord patient;
    if (search_patient_record(patient_id, &patient)) {
        fprintf(fp, "<h2>Medical Report for %s</h2>\n", patient.full_name);
        fprintf(fp, "<p>Patient ID: %d</p>\n", patient.id);
        fprintf(fp, "<p>Age: %d</p>\n", patient.age);
        fprintf(fp, "<p>Gender: %c</p>\n", patient.gender);
    }

    // Get medical history
    MedicalRecord records[50];
    int count = get_patient_medical_history(patient_id, records, 50);

    if (count > 0) {
        fprintf(fp, "<h3>Medical History</h3>\n");
        fprintf(fp, "<table>\n");
        fprintf(fp, "<tr><th>Date</th><th>Doctor</th><th>Diagnosis</th><th>Prescription</th><th>Test Results</th></tr>\n");

        for (int i = 0; i < count; i++) {
            // Get doctor name
            DoctorRecord doctor;
            char doctor_name[50] = "Unknown";
            if (search_doctor_record(records[i].doctor_id, &doctor)) {
                strncpy(doctor_name, doctor.name, sizeof(doctor_name)-1);
            }

            fprintf(fp, "<tr>\n");
            fprintf(fp, "<td>%s</td>\n", records[i].date);
            fprintf(fp, "<td>%s</td>\n", doctor_name);
            fprintf(fp, "<td>%s</td>\n", records[i].diagnosis);
            fprintf(fp, "<td>%s</td>\n", records[i].prescription);
            fprintf(fp, "<td>%s</td>\n", records[i].test_results);
            fprintf(fp, "</tr>\n");
        }
        fprintf(fp, "</table>\n");
    } else {
        fprintf(fp, "<p>No medical records found.</p>\n");
    }

    // Write HTML footer
    fprintf(fp, "</body>\n</html>\n");
    fclose(fp);
    return 1;
}

// Calculate total medical charges for a patient
float calculate_total_charges(int patient_id) {
    FILE *fp = fopen(MEDICAL_RECORDS_FILE, "rb");
    if (!fp) return 0.0f;

    float total = 0.0f;
    MedicalRecord rec;
    while (fread(&rec, sizeof(MedicalRecord), 1, fp)) {
        if (rec.patient_id == patient_id) {
            total += rec.charges;
        }
    }
    fclose(fp);
    return total;
}

// Generate prescription in PDF format (requires external PDF library)
int generate_prescription(const MedicalRecord *rec, const char *output_file) {
    FILE *fp = fopen(output_file, "w");
    if (!fp) return 0;

    // Get patient and doctor details
    PatientRecord patient;
    DoctorRecord doctor;
    search_patient_record(rec->patient_id, &patient);
    search_doctor_record(rec->doctor_id, &doctor);

    // Write prescription in a formatted text file (can be enhanced to PDF)
    fprintf(fp, "=== HOSPITAL PRESCRIPTION ===\n\n");
    fprintf(fp, "Date: %s\n\n", rec->date);
    fprintf(fp, "Patient Name: %s\n", patient.full_name);
    fprintf(fp, "Patient ID: %d\n", patient.id);
    fprintf(fp, "Age: %d\n", patient.age);
    fprintf(fp, "Gender: %c\n\n", patient.gender);
    fprintf(fp, "Doctor: Dr. %s\n", doctor.name);
    fprintf(fp, "Specialization: %s\n\n", doctor.specialization);
    fprintf(fp, "Diagnosis:\n%s\n\n", rec->diagnosis);
    fprintf(fp, "Prescription:\n%s\n\n", rec->prescription);
    fprintf(fp, "Notes:\n%s\n", rec->notes);

    fclose(fp);
    return 1;
}
