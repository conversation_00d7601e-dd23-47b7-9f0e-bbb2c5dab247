#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "../inc/functions.h"

#define STAFF_FILE "staff.dat"
#define DOCTOR_FILE "doctors.dat"
#define WARD_FILE "wards.dat"

// Staff Management Implementation
int add_staff_record(const StaffRecord *rec) {
    FILE *fp = fopen(STAFF_FILE, "ab");
    if (!fp) return 0;
    int success = fwrite(rec, sizeof(StaffRecord), 1, fp) == 1;
    fclose(fp);
    return success;
}

int update_staff_record(const StaffRecord *rec) {
    FILE *fp = fopen(STAFF_FILE, "r+b");
    if (!fp) return 0;

    StaffRecord temp;
    int found = 0;
    while (fread(&temp, sizeof(StaffRecord), 1, fp)) {
        if (temp.id == rec->id) {
            fseek(fp, -sizeof(StaffRecord), SEEK_CUR);
            found = fwrite(rec, sizeof(StaffRecord), 1, fp) == 1;
            break;
        }
    }
    fclose(fp);
    return found;
}

int delete_staff_record(int id) {
    FILE *fp = fopen(STAFF_FILE, "rb");
    FILE *temp = fopen("temp.dat", "wb");
    if (!fp || !temp) {
        if (fp) fclose(fp);
        if (temp) fclose(temp);
        return 0;
    }

    StaffRecord rec;
    int found = 0;
    while (fread(&rec, sizeof(StaffRecord), 1, fp)) {
        if (rec.id != id) {
            fwrite(&rec, sizeof(StaffRecord), 1, temp);
        } else {
            found = 1;
        }
    }
    fclose(fp);
    fclose(temp);
    remove(STAFF_FILE);
    rename("temp.dat", STAFF_FILE);
    return found;
}

int search_staff_record(int id, StaffRecord *rec) {
    FILE *fp = fopen(STAFF_FILE, "rb");
    if (!fp) return 0;

    int found = 0;
    while (fread(rec, sizeof(StaffRecord), 1, fp)) {
        if (rec->id == id) {
            found = 1;
            break;
        }
    }
    fclose(fp);
    return found;
}

void list_staff_records(void) {
    FILE *fp = fopen(STAFF_FILE, "rb");
    if (!fp) return;

    StaffRecord rec;
    printf("\nStaff Records:\n");
    printf("ID\tName\tPosition\tDepartment\tContact\tSalary\tJoining Date\n");
    printf("----------------------------------------------------------------\n");
    while (fread(&rec, sizeof(StaffRecord), 1, fp)) {
        printf("%d\t%s\t%s\t%s\t%s\t%.2f\t%s\n",
               rec.id, rec.name, rec.position, rec.department,
               rec.contact, rec.salary, rec.joining_date);
    }
    fclose(fp);
}

// Doctor Management Implementation
int add_doctor_record(const DoctorRecord *rec) {
    FILE *fp = fopen(DOCTOR_FILE, "ab");
    if (!fp) return 0;
    int success = fwrite(rec, sizeof(DoctorRecord), 1, fp) == 1;
    fclose(fp);
    return success;
}

int update_doctor_record(const DoctorRecord *rec) {
    FILE *fp = fopen(DOCTOR_FILE, "r+b");
    if (!fp) return 0;

    DoctorRecord temp;
    int found = 0;
    while (fread(&temp, sizeof(DoctorRecord), 1, fp)) {
        if (temp.id == rec->id) {
            fseek(fp, -sizeof(DoctorRecord), SEEK_CUR);
            found = fwrite(rec, sizeof(DoctorRecord), 1, fp) == 1;
            break;
        }
    }
    fclose(fp);
    return found;
}

int delete_doctor_record(int id) {
    FILE *fp = fopen(DOCTOR_FILE, "rb");
    FILE *temp = fopen("temp.dat", "wb");
    if (!fp || !temp) {
        if (fp) fclose(fp);
        if (temp) fclose(temp);
        return 0;
    }

    DoctorRecord rec;
    int found = 0;
    while (fread(&rec, sizeof(DoctorRecord), 1, fp)) {
        if (rec.id != id) {
            fwrite(&rec, sizeof(DoctorRecord), 1, temp);
        } else {
            found = 1;
        }
    }

    fclose(fp);
    fclose(temp);
    remove(DOCTOR_FILE);
    rename("temp.dat", DOCTOR_FILE);
    return found;
}

int search_doctor_record(int id, DoctorRecord *rec) {
    FILE *fp = fopen(DOCTOR_FILE, "rb");
    if (!fp) return 0;

    int found = 0;
    while (fread(rec, sizeof(DoctorRecord), 1, fp)) {
        if (rec->id == id) {
            found = 1;
            break;
        }
    }
    fclose(fp);
    return found;
}

void list_doctor_records(void) {
    FILE *fp = fopen(DOCTOR_FILE, "rb");
    if (!fp) return;

    DoctorRecord rec;
    printf("\nDoctor Records:\n");
    printf("ID\tName\tSpecialization\tQualification\tContact\tSalary\tSchedule\tRoom\n");
    printf("--------------------------------------------------------------------------------\n");
    while (fread(&rec, sizeof(DoctorRecord), 1, fp)) {
        printf("%d\t%s\t%s\t%s\t%s\t%.2f\t%s\t%d\n",
               rec.id, rec.name, rec.specialization, rec.qualification,
               rec.contact, rec.salary, rec.schedule, rec.room_no);
    }
    fclose(fp);
}

// Ward Management Implementation
int add_ward_record(const WardRecord *rec) {
    FILE *fp = fopen(WARD_FILE, "ab");
    if (!fp) return 0;
    int success = fwrite(rec, sizeof(WardRecord), 1, fp) == 1;
    fclose(fp);
    return success;
}

int update_ward_record(const WardRecord *rec) {
    FILE *fp = fopen(WARD_FILE, "r+b");
    if (!fp) return 0;

    WardRecord temp;
    int found = 0;
    while (fread(&temp, sizeof(WardRecord), 1, fp)) {
        if (temp.id == rec->id) {
            fseek(fp, -sizeof(WardRecord), SEEK_CUR);
            found = fwrite(rec, sizeof(WardRecord), 1, fp) == 1;
            break;
        }
    }
    fclose(fp);
    return found;
}

int delete_ward_record(int id) {
    FILE *fp = fopen(WARD_FILE, "rb");
    FILE *temp = fopen("temp.dat", "wb");
    if (!fp || !temp) {
        if (fp) fclose(fp);
        if (temp) fclose(temp);
        return 0;
    }

    WardRecord rec;
    int found = 0;
    while (fread(&rec, sizeof(WardRecord), 1, fp)) {
        if (rec.id != id) {
            fwrite(&rec, sizeof(WardRecord), 1, temp);
        } else {
            found = 1;
        }
    }

    fclose(fp);
    fclose(temp);
    remove(WARD_FILE);
    rename("temp.dat", WARD_FILE);
    return found;
}

int search_ward_record(int id, WardRecord *rec) {
    FILE *fp = fopen(WARD_FILE, "rb");
    if (!fp) return 0;

    int found = 0;
    while (fread(rec, sizeof(WardRecord), 1, fp)) {
        if (rec->id == id) {
            found = 1;
            break;
        }
    }
    fclose(fp);
    return found;
}

void list_ward_records(void) {
    FILE *fp = fopen(WARD_FILE, "rb");
    if (!fp) return;

    WardRecord rec;
    printf("\nWard Records:\n");
    printf("ID\tName\tTotal Beds\tOccupied\tNurse In-charge\tType\n");
    printf("----------------------------------------------------------------\n");
    while (fread(&rec, sizeof(WardRecord), 1, fp)) {
        printf("%d\t%s\t%d\t%d\t%s\t%s\n",
               rec.id, rec.name, rec.total_beds, rec.occupied_beds,
               rec.nurse_incharge, rec.type);
    }
    fclose(fp);
}

// Statistics functions
int get_total_staff(void) {
    FILE *fp = fopen(STAFF_FILE, "rb");
    if (!fp) return 0;

    int count = 0;
    StaffRecord rec;
    while (fread(&rec, sizeof(StaffRecord), 1, fp)) {
        count++;
    }
    fclose(fp);
    return count;
}

int get_total_doctors(void) {
    FILE *fp = fopen(DOCTOR_FILE, "rb");
    if (!fp) return 0;

    int count = 0;
    DoctorRecord rec;
    while (fread(&rec, sizeof(DoctorRecord), 1, fp)) {
        count++;
    }
    fclose(fp);
    return count;
}

int get_total_wards(void) {
    FILE *fp = fopen(WARD_FILE, "rb");
    if (!fp) return 0;

    int count = 0;
    WardRecord rec;
    while (fread(&rec, sizeof(WardRecord), 1, fp)) {
        count++;
    }
    fclose(fp);
    return count;
}

int get_available_beds(void) {
    FILE *fp = fopen(WARD_FILE, "rb");
    if (!fp) return 0;

    int available = 0;
    WardRecord rec;
    while (fread(&rec, sizeof(WardRecord), 1, fp)) {
        available += (rec.total_beds - rec.occupied_beds);
    }
    fclose(fp);
    return available;
}
