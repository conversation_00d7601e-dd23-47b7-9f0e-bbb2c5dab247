#include <gtk/gtk.h>
#include "../inc/functions.h"
#include "../inc/dashboard.h"
#include "../inc/gui_helpers.h"
#include "../gui.h"

// Global signal connection helper functions
void connect_signals(GtkWidget *window, GtkBuilder *builder) {
    // Get widgets
    GtkWidget *btn_add = GTK_WIDGET(gtk_builder_get_object(builder, "btn_add"));
    GtkWidget *btn_delete = GTK_WIDGET(gtk_builder_get_object(builder, "btn_delete"));
    GtkWidget *btn_search = GTK_WIDGET(gtk_builder_get_object(builder, "btn_search"));
    GtkWidget *btn_view = GTK_WIDGET(gtk_builder_get_object(builder, "btn_view"));
    GtkWidget *btn_update = GTK_WIDGET(gtk_builder_get_object(builder, "btn_update"));
    GtkWidget *btn_room = GTK_WIDGET(gtk_builder_get_object(builder, "btn_room"));
    GtkWidget *btn_payment = GTK_WIDGET(gtk_builder_get_object(builder, "btn_payment"));
    GtkWidget *btn_export = GTK_WIDGET(gtk_builder_get_object(builder, "btn_export"));

    // Connect signals
    g_signal_connect(window, "destroy", G_CALLBACK(gtk_main_quit), NULL);
    g_signal_connect(btn_add, "clicked", G_CALLBACK(on_add_clicked), NULL);
    g_signal_connect(btn_delete, "clicked", G_CALLBACK(on_delete_clicked), NULL);
    g_signal_connect(btn_search, "clicked", G_CALLBACK(on_search_clicked), NULL);
    g_signal_connect(btn_view, "clicked", G_CALLBACK(on_view_clicked), NULL);
    g_signal_connect(btn_update, "clicked", G_CALLBACK(on_update_clicked), NULL);
    g_signal_connect(btn_room, "clicked", G_CALLBACK(on_room_clicked), NULL);
    g_signal_connect(btn_payment, "clicked", G_CALLBACK(on_payment_clicked), NULL);
    g_signal_connect(btn_export, "clicked", G_CALLBACK(on_export_csv_clicked), NULL);
}

// Helper functions declared in gui_helpers.h
void show_error_message(const char *message) {
    GtkWidget *dialog = gtk_message_dialog_new(NULL,
                                             GTK_DIALOG_MODAL,
                                             GTK_MESSAGE_ERROR,
                                             GTK_BUTTONS_OK,
                                             "%s", message);
    gtk_dialog_run(GTK_DIALOG(dialog));
    gtk_widget_destroy(dialog);
}

void show_success_message(const char *message) {
    GtkWidget *dialog = gtk_message_dialog_new(NULL,
                                             GTK_DIALOG_MODAL,
                                             GTK_MESSAGE_INFO,
                                             GTK_BUTTONS_OK,
                                             "%s", message);
    gtk_dialog_run(GTK_DIALOG(dialog));
    gtk_widget_destroy(dialog);
}

void create_stats_label(const char *text, const char *value, GtkGrid *grid, int row) {
    GtkWidget *label_text = gtk_label_new(text);
    GtkWidget *label_value = gtk_label_new(value);
    gtk_widget_set_halign(label_text, GTK_ALIGN_START);
    gtk_widget_set_halign(label_value, GTK_ALIGN_END);
    gtk_grid_attach(grid, label_text, 0, row, 1, 1);
    gtk_grid_attach(grid, label_value, 1, row, 1, 1);
}

// Placeholder implementations for missing GUI callbacks
void on_add_staff_clicked(GtkButton *button, gpointer user_data) {
    show_success_message("Add staff functionality - Coming soon!");
}

void on_view_staff_clicked(GtkButton *button, gpointer user_data) {
    show_success_message("View staff functionality - Coming soon!");
}

void on_update_staff_clicked(GtkButton *button, gpointer user_data) {
    show_success_message("Update staff functionality - Coming soon!");
}

void on_delete_staff_clicked(GtkButton *button, gpointer user_data) {
    show_success_message("Delete staff functionality - Coming soon!");
}

void on_add_ward_clicked(GtkButton *button, gpointer user_data) {
    show_success_message("Add ward functionality - Coming soon!");
}

void on_view_ward_clicked(GtkButton *button, gpointer user_data) {
    show_success_message("View ward functionality - Coming soon!");
}

void on_update_ward_clicked(GtkButton *button, gpointer user_data) {
    show_success_message("Update ward functionality - Coming soon!");
}

void on_delete_ward_clicked(GtkButton *button, gpointer user_data) {
    show_success_message("Delete ward functionality - Coming soon!");
}
