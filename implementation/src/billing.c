#include "../inc/functions.h"
#include <stdlib.h>

// Implementation of insurance record functions
int get_patient_insurance(int patient_id, InsuranceRecord *insurance) {
    FILE *fp = fopen("insurance.dat", "rb");
    if (!fp) return -1;

    InsuranceRecord temp;
    int found = 0;

    while (fread(&temp, sizeof(InsuranceRecord), 1, fp)) {
        if (temp.patient_id == patient_id) {
            *insurance = temp;
            found = 1;
            break;
        }
    }

    fclose(fp);
    return found ? 0 : -1;
}

int update_insurance_record(const InsuranceRecord *insurance) {
    FILE *fp = fopen("insurance.dat", "r+b");
    if (!fp) return -1;

    InsuranceRecord temp;
    int found = 0;
    long pos;

    while (fread(&temp, sizeof(InsuranceRecord), 1, fp)) {
        if (temp.patient_id == insurance->patient_id) {
            pos = ftell(fp) - sizeof(InsuranceRecord);
            fseek(fp, pos, SEEK_SET);
            fwrite(insurance, sizeof(InsuranceRecord), 1, fp);
            found = 1;
            break;
        }
    }

    fclose(fp);
    return found ? 0 : -1;
}

// Function to generate a bill for a patient
void generate_bill(int patient_id) {
    // Get patient insurance info
    InsuranceRecord insurance;
    int has_insurance = (get_patient_insurance(patient_id, &insurance) == 0);

    // Calculate total charges (example values)
    double room_charges = 1000.0;
    double medicine_charges = 500.0;
    double doctor_fees = 300.0;
    double misc_charges = 200.0;
    double total = room_charges + medicine_charges + doctor_fees + misc_charges;

    // Apply insurance if available
    double insurance_coverage = 0.0;
    if (has_insurance) {
        insurance_coverage = (insurance.coverage_amount < total) ?
                            insurance.coverage_amount : total;
        total -= insurance_coverage;
    }

    // Save bill to file
    FILE *fp = fopen("bills.csv", "a");
    if (fp) {
        fprintf(fp, "%d,%.2f,%.2f,%.2f,%.2f,%.2f,%.2f,%.2f\n",
               patient_id, room_charges, medicine_charges, doctor_fees,
               misc_charges, insurance_coverage, total,
               room_charges + medicine_charges + doctor_fees + misc_charges);
        fclose(fp);
    }
}
