#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include "../inc/functions.h"

// Appointment Management Implementation
int add_appointment_record(const AppointmentRecord *rec) {
    if (!rec || !validate_id(rec->id) || !validate_id(rec->patient_id) || !validate_id(rec->doctor_id)) {
        return -1;
    }

    FILE *fp = fopen(APPOINTMENTS_FILE, "ab");
    if (!fp) return -1;

    int success = fwrite(rec, sizeof(AppointmentRecord), 1, fp) == 1;
    fclose(fp);
    return success ? 0 : -1;
}

int add_appointment(const AppointmentRecord *rec) {
    // Check if the time slot is available
    FILE *fp = fopen(APPOINTMENTS_FILE, "rb");
    if (fp) {
        AppointmentRecord temp;
        while (fread(&temp, sizeof(AppointmentRecord), 1, fp)) {
            if (temp.doctor_id == rec->doctor_id &&
                strcmp(temp.date, rec->date) == 0 &&
                strcmp(temp.time, rec->time) == 0) {
                fclose(fp);
                return 0; // Time slot already booked
            }
        }
        fclose(fp);
    }

    // Add new appointment
    fp = fopen(APPOINTMENTS_FILE, "ab");
    if (!fp) return 0;
    int success = fwrite(rec, sizeof(AppointmentRecord), 1, fp) == 1;
    fclose(fp);
    return success;
}

int update_appointment_status(int id, const char *status) {
    FILE *fp = fopen(APPOINTMENTS_FILE, "r+b");
    if (!fp) return 0;

    AppointmentRecord rec;
    int found = 0;
    while (fread(&rec, sizeof(AppointmentRecord), 1, fp)) {
        if (rec.id == id) {
            strncpy(rec.status, status, sizeof(rec.status)-1);
            fseek(fp, -sizeof(AppointmentRecord), SEEK_CUR);
            found = fwrite(&rec, sizeof(AppointmentRecord), 1, fp) == 1;
            break;
        }
    }
    fclose(fp);
    return found;
}

int get_doctor_appointments(int doctor_id, const char *date, AppointmentRecord *appointments, int max_count) {
    FILE *fp = fopen(APPOINTMENTS_FILE, "rb");
    if (!fp) return 0;

    int count = 0;
    AppointmentRecord rec;
    while (fread(&rec, sizeof(AppointmentRecord), 1, fp) && count < max_count) {
        if (rec.doctor_id == doctor_id && strcmp(rec.date, date) == 0) {
            appointments[count++] = rec;
        }
    }
    fclose(fp);
    return count;
}

int get_patient_appointments(int patient_id, AppointmentRecord *appointments, int max_count) {
    FILE *fp = fopen(APPOINTMENTS_FILE, "rb");
    if (!fp) return 0;

    int count = 0;
    AppointmentRecord rec;
    while (fread(&rec, sizeof(AppointmentRecord), 1, fp) && count < max_count) {
        if (rec.patient_id == patient_id) {
            appointments[count++] = rec;
        }
    }
    fclose(fp);
    return count;
}

// Get available time slots for a doctor on a specific date
int get_available_slots(int doctor_id, const char *date, char slots[][10], int max_slots) {
    // Define working hours (8 AM to 5 PM, 30-minute slots)
    const char *working_hours[] = {
        "08:00", "08:30", "09:00", "09:30", "10:00", "10:30",
        "11:00", "11:30", "12:00", "12:30", "13:00", "13:30",
        "14:00", "14:30", "15:00", "15:30", "16:00", "16:30"
    };
    int total_slots = sizeof(working_hours) / sizeof(working_hours[0]);

    // Get booked appointments
    AppointmentRecord booked[50];
    int booked_count = get_doctor_appointments(doctor_id, date, booked, 50);

    // Find available slots
    int available_count = 0;
    for (int i = 0; i < total_slots && available_count < max_slots; i++) {
        int is_available = 1;
        for (int j = 0; j < booked_count; j++) {
            if (strcmp(booked[j].time, working_hours[i]) == 0) {
                is_available = 0;
                break;
            }
        }
        if (is_available) {
            strcpy(slots[available_count++], working_hours[i]);
        }
    }
    return available_count;
}

// Generate unique appointment ID
int generate_appointment_id(void) {
    static int last_id = 0;
    if (last_id == 0) {
        // Initialize from file
        FILE *fp = fopen(APPOINTMENTS_FILE, "rb");
        if (fp) {
            AppointmentRecord rec;
            while (fread(&rec, sizeof(AppointmentRecord), 1, fp)) {
                if (rec.id > last_id) last_id = rec.id;
            }
            fclose(fp);
        }
    }
    return ++last_id;
}

// Additional functions to match header declarations
int update_appointment_record(const AppointmentRecord *rec) {
    if (!rec || !validate_id(rec->id)) return -1;

    FILE *fp = fopen(APPOINTMENTS_FILE, "r+b");
    if (!fp) return -1;

    AppointmentRecord temp;
    int found = 0;
    while (fread(&temp, sizeof(AppointmentRecord), 1, fp)) {
        if (temp.id == rec->id) {
            fseek(fp, -sizeof(AppointmentRecord), SEEK_CUR);
            found = fwrite(rec, sizeof(AppointmentRecord), 1, fp) == 1;
            break;
        }
    }
    fclose(fp);
    return found ? 0 : -1;
}

int delete_appointment_record(int id) {
    if (!validate_id(id)) return -1;

    FILE *fp = fopen(APPOINTMENTS_FILE, "rb");
    FILE *temp = fopen("temp_appointments.dat", "wb");
    if (!fp || !temp) {
        if (fp) fclose(fp);
        if (temp) fclose(temp);
        return -1;
    }

    AppointmentRecord rec;
    int found = 0;
    while (fread(&rec, sizeof(AppointmentRecord), 1, fp)) {
        if (rec.id != id) {
            fwrite(&rec, sizeof(AppointmentRecord), 1, temp);
        } else {
            found = 1;
        }
    }

    fclose(fp);
    fclose(temp);
    remove(APPOINTMENTS_FILE);
    rename("temp_appointments.dat", APPOINTMENTS_FILE);
    return found ? 0 : -1;
}

int search_appointment_record(int id, AppointmentRecord *rec) {
    if (!rec || !validate_id(id)) return -1;

    FILE *fp = fopen(APPOINTMENTS_FILE, "rb");
    if (!fp) return -1;

    int found = 0;
    while (fread(rec, sizeof(AppointmentRecord), 1, fp)) {
        if (rec->id == id) {
            found = 1;
            break;
        }
    }
    fclose(fp);
    return found ? 0 : -1;
}

void list_appointment_records(void) {
    FILE *fp = fopen(APPOINTMENTS_FILE, "rb");
    if (!fp) {
        printf("No appointment records found.\n");
        return;
    }

    AppointmentRecord rec;
    printf("\nAppointment Records:\n");
    printf("%-5s | %-10s | %-10s | %-12s | %-6s | %-15s | %-20s\n",
           "ID", "Patient ID", "Doctor ID", "Date", "Time", "Status", "Notes");
    printf("--------------------------------------------------------------------------------\n");

    int count = 0;
    while (fread(&rec, sizeof(AppointmentRecord), 1, fp)) {
        printf("%-5d | %-10d | %-10d | %-12s | %-6s | %-15s | %-20s\n",
               rec.id, rec.patient_id, rec.doctor_id, rec.date,
               rec.time, rec.status, rec.notes);
        count++;
    }

    fclose(fp);
    printf("\nTotal appointments: %d\n", count);
}

int get_total_appointments(void) {
    FILE *fp = fopen(APPOINTMENTS_FILE, "rb");
    if (!fp) return 0;

    int count = 0;
    AppointmentRecord rec;
    while (fread(&rec, sizeof(AppointmentRecord), 1, fp)) {
        count++;
    }
    fclose(fp);
    return count;
}
