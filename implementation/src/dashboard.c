#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include "../inc/functions.h"
#include <gtk/gtk.h>
#include "../inc/dashboard.h"

// Helper function declarations
static int load_historical_data(double *values, int max_days);

// Helper function to get current date as string (YYYY-MM-DD)
void get_current_date(char *date_str) {
    time_t t = time(NULL);
    struct tm tm = *localtime(&t);
    sprintf(date_str, "%04d-%02d-%02d",
            tm.tm_year + 1900, tm.tm_mon + 1, tm.tm_mday);
}

// Calculate department statistics
void calculate_department_stats(DashboardStats *stats) {
    // Initialize departments
    const char *departments[] = {"General", "Cardiology", "Orthopedics", "Pediatrics", "Neurology"};
    stats->department_count = sizeof(departments) / sizeof(departments[0]);

    for (int i = 0; i < stats->department_count; i++) {
        strcpy(stats->department_stats[i].name, departments[i]);
        stats->department_stats[i].patients = 0;
        stats->department_stats[i].revenue = 0.0f;
    }

    FILE *fp = fopen(MEDICAL_RECORDS_FILE, "rb");
    if (fp) {
        MedicalRecord rec;
        DoctorRecord doc;
        while (fread(&rec, sizeof(MedicalRecord), 1, fp) == 1) {
            if (search_doctor_record(rec.doctor_id, &doc)) {
                for (int i = 0; i < stats->department_count; i++) {
                    if (strstr(doc.specialization, stats->department_stats[i].name)) {
                        stats->department_stats[i].patients++;
                        stats->department_stats[i].revenue += rec.charges;
                        break;
                    }
                }
            }
        }
        fclose(fp);
    }
}

// Calculate financial statistics
void calculate_financial_stats(DashboardStats *stats) {
    char today[11];
    get_current_date(today);

    stats->total_revenue = 0;
    stats->pending_payments = 0;
    stats->today_collections = 0;

    FILE *fp = fopen(BILLING_FILE, "rb");
    if (fp) {
        BillingRecord bill;
        while (fread(&bill, sizeof(BillingRecord), 1, fp) == 1) {
            stats->total_revenue += bill.total_amount;
            if (strcmp(bill.payment_status, "Pending") == 0) {
                stats->pending_payments += bill.patient_payable;
            }
            if (strcmp(bill.date, today) == 0 && strcmp(bill.payment_status, "Paid") == 0) {
                stats->today_collections += bill.total_amount;
            }
        }
        fclose(fp);
    }

    // Calculate insurance claims
    stats->insurance_claims = 0;
    fp = fopen(INSURANCE_FILE, "rb");
    if (fp) {
        InsuranceRecord ins;
        while (fread(&ins, sizeof(InsuranceRecord), 1, fp) == 1) {
            // Check if insurance is still valid by comparing expiry date
            char today[11];
            get_current_date(today);
            if (strcmp(ins.expiry_date, today) > 0) {  // expiry_date is in the future
                stats->insurance_claims += ins.coverage_amount;
            }
        }
        fclose(fp);
    }
}

// Get complete dashboard statistics
void get_dashboard_stats(DashboardStats *stats) {
    memset(stats, 0, sizeof(DashboardStats));
    char today[11];
    get_current_date(today);

    // Patient Statistics
    stats->total_patients = get_total_patients();
    stats->admitted_patients = 0;
    stats->outpatients = 0;
    stats->discharged_today = 0;
    stats->new_registrations = 0;

    FILE *fp = fopen("Assignment.dat", "r");
    if (fp) {
        PatientRecord rec;
        while (fread(&rec, sizeof(PatientRecord), 1, fp) == 1) {
            if (rec.room > 0) stats->admitted_patients++;
            else stats->outpatients++;
        }
        fclose(fp);
    }

    // Doctor Statistics
    stats->total_doctors = get_total_doctors();
    stats->doctors_present = 0;
    stats->scheduled_appointments = 0;

    fp = fopen(APPOINTMENTS_FILE, "rb");
    if (fp) {
        AppointmentRecord app;
        while (fread(&app, sizeof(AppointmentRecord), 1, fp) == 1) {
            if (strcmp(app.date, today) == 0) {
                stats->scheduled_appointments++;
            }
        }
        fclose(fp);
    }

    // Ward Statistics
    stats->total_wards = get_total_wards();
    stats->total_beds = 0;
    stats->occupied_beds = 0;

    fp = fopen(WARD_FILE, "rb");
    if (fp) {
        WardRecord ward;
        while (fread(&ward, sizeof(WardRecord), 1, fp) == 1) {
            stats->total_beds += ward.total_beds;
            stats->occupied_beds += ward.occupied_beds;
        }
        fclose(fp);
    }

    stats->available_beds = stats->total_beds - stats->occupied_beds;
    stats->bed_occupancy_rate = stats->total_beds > 0 ?
        ((float)stats->occupied_beds / stats->total_beds) * 100 : 0;

    // Emergency Statistics (from today's records)
    stats->emergency_cases = 0;
    stats->critical_patients = 0;

    fp = fopen(MEDICAL_RECORDS_FILE, "rb");
    if (fp) {
        MedicalRecord rec;
        while (fread(&rec, sizeof(MedicalRecord), 1, fp) == 1) {
            if (strcmp(rec.date, today) == 0) {
                if (strstr(rec.notes, "EMERGENCY")) stats->emergency_cases++;
                if (strstr(rec.notes, "CRITICAL")) stats->critical_patients++;
            }
        }
        fclose(fp);
    }

    // Calculate department and financial statistics
    calculate_department_stats(stats);
    calculate_financial_stats(stats);
}

// Draw a simple bar chart
static void draw_bar_chart(cairo_t *cr, double x, double y, double width, double height,
                          double value, double max_value, const char *label) {
    // Draw bar background
    cairo_set_source_rgb(cr, 0.9, 0.9, 0.9);
    cairo_rectangle(cr, x, y, width, height);
    cairo_fill(cr);

    // Draw bar value
    cairo_set_source_rgb(cr, 0.2, 0.6, 0.9);
    double bar_width = (value / max_value) * width;
    cairo_rectangle(cr, x, y, bar_width, height);
    cairo_fill(cr);

    // Draw label
    cairo_set_source_rgb(cr, 0.0, 0.0, 0.0);
    cairo_select_font_face(cr, "Sans", CAIRO_FONT_SLANT_NORMAL, CAIRO_FONT_WEIGHT_NORMAL);
    cairo_set_font_size(cr, 12.0);
    cairo_move_to(cr, x, y - 5);
    cairo_show_text(cr, label);

    // Draw value
    char value_text[32];
    snprintf(value_text, sizeof(value_text), "%.1f", value);
    cairo_move_to(cr, x + bar_width + 5, y + height/2);
    cairo_show_text(cr, value_text);
}

// Draw a pie chart
static void draw_pie_chart(cairo_t *cr, double x, double y, double radius,
                          double value, double total, const char *label) {
    double percentage = value / total;
    double angle = percentage * 2 * G_PI;

    // Draw background circle
    cairo_set_source_rgb(cr, 0.9, 0.9, 0.9);
    cairo_arc(cr, x, y, radius, 0, 2 * G_PI);
    cairo_fill(cr);

    // Draw pie slice
    cairo_set_source_rgb(cr, 0.2, 0.6, 0.9);
    cairo_move_to(cr, x, y);
    cairo_arc(cr, x, y, radius, 0, angle);
    cairo_close_path(cr);
    cairo_fill(cr);

    // Draw label
    cairo_set_source_rgb(cr, 0.0, 0.0, 0.0);
    cairo_select_font_face(cr, "Sans", CAIRO_FONT_SLANT_NORMAL, CAIRO_FONT_WEIGHT_NORMAL);
    cairo_set_font_size(cr, 12.0);
    cairo_move_to(cr, x - radius, y + radius + 20);
    char text[64];
    snprintf(text, sizeof(text), "%s: %.1f%%", label, percentage * 100);
    cairo_show_text(cr, text);
}

// Draw line graph for historical data
static void draw_line_graph(cairo_t *cr, double x, double y, double width, double height,
                           const double *values, int count, const char *label) {
    if (count < 2) return;

    // Find max value
    double max_value = values[0];
    for (int i = 1; i < count; i++) {
        if (values[i] > max_value) max_value = values[i];
    }

    // Draw axes
    cairo_set_source_rgb(cr, 0.0, 0.0, 0.0);
    cairo_move_to(cr, x, y + height);
    cairo_line_to(cr, x + width, y + height);
    cairo_move_to(cr, x, y);
    cairo_line_to(cr, x, y + height);
    cairo_stroke(cr);

    // Draw graph
    cairo_set_source_rgb(cr, 0.2, 0.6, 0.9);
    double x_step = width / (count - 1);
    double y_scale = height / max_value;

    cairo_move_to(cr, x, y + height - (values[0] * y_scale));
    for (int i = 1; i < count; i++) {
        cairo_line_to(cr, x + (i * x_step), y + height - (values[i] * y_scale));
    }
    cairo_stroke(cr);

    // Draw label
    cairo_set_source_rgb(cr, 0.0, 0.0, 0.0);
    cairo_move_to(cr, x, y - 10);
    cairo_show_text(cr, label);
}

// Create visualization area
GtkWidget* create_visualization_area(const DashboardStats *stats) {
    GtkWidget *drawing_area = gtk_drawing_area_new();
    gtk_widget_set_size_request(drawing_area, 600, 400);

    g_signal_connect(G_OBJECT(drawing_area), "draw",
                    G_CALLBACK(on_draw_callback), (gpointer)stats);

    return drawing_area;
}

// Drawing callback function implementation
gboolean on_draw_callback(GtkWidget *widget, cairo_t *cr, gpointer data) {
    DashboardStats *stats = (DashboardStats *)data;
    GtkAllocation allocation;
    gtk_widget_get_allocation(widget, &allocation);

    int width = allocation.width;
    int height = allocation.height;

    // Draw bed occupancy pie chart
    draw_pie_chart(cr, width/4, height/4, 50,
                  stats->occupied_beds, stats->total_beds,
                  "Bed Occupancy");

    // Draw revenue bar chart
    draw_bar_chart(cr, 50, height/2, width-100, 20,
                  stats->total_revenue, stats->total_revenue + stats->pending_payments,
                  "Revenue");

    // Draw historical data if available
    double historical_values[7] = {0}; // Last 7 days
    int count = load_historical_data(historical_values, 7);
    if (count > 0) {
        draw_line_graph(cr, 50, height*3/4, width-100, 100,
                       historical_values, count, "Weekly Trend");
    }

    return FALSE;
}

// Load historical data implementation
static int load_historical_data(double *values, int max_days) {
    FILE *fp = fopen("statistics_history.csv", "r");
    if (!fp) return 0;

    int count = 0;
    char line[256];

    // Skip to last max_days lines
    long pos = -1;
    int lines = 0;
    while (fgets(line, sizeof(line), fp)) lines++;

    if (lines <= max_days) {
        rewind(fp);
    } else {
        fseek(fp, pos, SEEK_END);
        while (lines > max_days && pos != 0) {
            char ch;
            while (pos >= 0) {
                fseek(fp, pos, SEEK_SET);
                ch = fgetc(fp);
                if (ch == '\n') {
                    lines--;
                    break;
                }
                pos--;
            }
        }
    }

    // Read values
    while (fgets(line, sizeof(line), fp) && count < max_days) {
        char *token = strtok(line, ",");
        if (!token) continue;

        // Skip date
        token = strtok(NULL, ",");
        if (!token) continue;

        values[count++] = atof(token);
    }

    fclose(fp);
    return count;
}

int count_records(const char *filename) {
    FILE *file = fopen(filename, "r");
    if (!file) return 0;

    int count = 0;
    char line[256];
    while (fgets(line, sizeof(line), file)) {
        count++;
    }
    fclose(file);
    return count;
}

double calculate_total_revenue(const char *filename) {
    FILE *file = fopen(filename, "r");
    if (!file) return 0.0;

    double total = 0.0;
    char line[256];
    while (fgets(line, sizeof(line), file)) {
        char *token = strtok(line, ","); // Skip Patient ID
        token = strtok(NULL, ",");       // Amount
        if (token) {
            total += atof(token);
        }
    }
    fclose(file);
    return total;
}

int process_uploaded_file(const char *filename) {
    // Placeholder implementation for processing uploaded files
    // Extend this function to parse and integrate data as needed
    FILE *file = fopen(filename, "r");
    if (!file) return 0;

    // Example: Just count lines to simulate processing
    int count = 0;
    char line[256];
    while (fgets(line, sizeof(line), file)) {
        count++;
    }
    fclose(file);
    return count > 0;
}
