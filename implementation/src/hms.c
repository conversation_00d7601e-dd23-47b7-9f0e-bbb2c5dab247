#include "../inc/functions.h"
#include "../inc/management.h"
#include <stdlib.h>
#include <time.h>

// Global variables - made static for better encapsulation
static int i=0, valid=0;
static char Esc=0x1b;
static int target=0;
static int search=0;
static int found=0;

// Legacy structure for backward compatibility
struct customer
{
    char full_name[50];
    char address[100];
    char gender;
    char date[20];
    char time;
    int id;
    int age;
    char contact[15];
    int room;
} ad, nw;
//list of global variable
void menu()
{
    system("clear");
	int select;
	printf("\n\n******************* DATA RECORDS *********************");
	printf("\n");
	printf("\n");
	printf("\n");
	printf("\n");
	printf(" \xDB\xDB\xDB\xDB\xDB\xDB\xB2\xB2\xB2\xB2\xB2\xB2	1. Add Records      \xB2\xB2\xB2\xB2\xB2\xB2\xDB\xDB\xDB\xDB\xDB\xDB  \n\n");
	printf(" \xDB\xDB\xDB\xDB\xDB\xDB\xB2\xB2\xB2\xB2\xB2\xB2	2. Delete Records   \xB2\xB2\xB2\xB2\xB2\xB2\xDB\xDB\xDB\xDB\xDB\xDB  \n\n");
	printf(" \xDB\xDB\xDB\xDB\xDB\xDB\xB2\xB2\xB2\xB2\xB2\xB2	3. Search Records   \xB2\xB2\xB2\xB2\xB2\xB2\xDB\xDB\xDB\xDB\xDB\xDB  \n\n");
	printf(" \xDB\xDB\xDB\xDB\xDB\xDB\xB2\xB2\xB2\xB2\xB2\xB2	4. View Records     \xB2\xB2\xB2\xB2\xB2\xB2\xDB\xDB\xDB\xDB\xDB\xDB  \n\n");
	printf(" \xDB\xDB\xDB\xDB\xDB\xDB\xB2\xB2\xB2\xB2\xB2\xB2	5. Update Info	    \xB2\xB2\xB2\xB2\xB2\xB2\xDB\xDB\xDB\xDB\xDB\xDB  \n\n");
	printf(" \xDB\xDB\xDB\xDB\xDB\xDB\xB2\xB2\xB2\xB2\xB2\xB2	6. Room	            \xB2\xB2\xB2\xB2\xB2\xB2\xDB\xDB\xDB\xDB\xDB\xDB  \n\n");
	printf(" \xDB\xDB\xDB\xDB\xDB\xDB\xB2\xB2\xB2\xB2\xB2\xB2	7. Exit	            \xB2\xB2\xB2\xB2\xB2\xB2\xDB\xDB\xDB\xDB\xDB\xDB  \n\n");
	printf(" \xDB\xDB\xDB\xDB\xDB\xDB\xB2\xB2\xB2\xB2\xB2\xB2	8. Search by Name   \xB2\xB2\xB2\xB2\xB2\xB2\xDB\xDB\xDB\xDB\xDB\xDB  \n\n");

	printf(" Choose a given number to procceed furthur : ");
	scanf("%d", &select);
	switch(select)
	{
		case 1:
			add();
			break;
			
		case 2:
			remove_record();
			break;
		
		case 3:
			find();
			break;
		
		case 4:
			p_list();
			break;
		
		case 5:
			edit();
			break;
		
		case 6:
			room();	
			break;
		
		case 7:
			exitt();
			break;

		case 8:
			find_by_name();
			break;

		default:
			printf("\nInvalid entry!!! Input a number between given choice.");
			getchar();
			menu();
			
	}
}


void add()
{
    system("clear");
    printf("\n\n ===============   ENTER DETAILS   =============== \n\n");

    FILE *fz;
    fz = fopen(ASSIGNMENT_FILE, "a");
    if (!fz) {
        printf("Error: Cannot open data file!\n");
        getchar();
        menu();
        return;
    }
    printf("\n");

    I:
    fclose(fz);
    if((fz = fopen(ASSIGNMENT_FILE, "r+")) != NULL) {
        printf("\n ID         : ");
        if (scanf("%i", &nw.id) != 1) {
            printf("Invalid input for ID!\n");
            fclose(fz);
            getchar();
            menu();
            return;
        }

        // Check for duplicate ID
        while(fscanf(fz, "%i %49s %99s %i %c %14s %i \n", &ad.id, ad.full_name, ad.address, &ad.age, &ad.gender, ad.contact, &ad.room) != EOF) {
            if(nw.id == ad.id) {
                printf("\n The ID number already exists.\n");
                fclose(fz);
                goto I;
            }
        }
        ad.id = nw.id;
        fclose(fz);
    }
    else {
        printf("\n ID         : ");
        if (scanf("%i", &ad.id) != 1) {
            printf("Invalid input for ID!\n");
            getchar();
            menu();
            return;
        }
    }
    printf("\n");

    // Clear input buffer
    while (getchar() != '\n');

    printf(" Name       : ");
    name_valid_check();
    printf("\n");

    printf(" Address    : ");
    if (fgets(ad.address, sizeof(ad.address), stdin) == NULL) {
        printf("Error reading address!\n");
        menu();
        return;
    }
    ad.address[strcspn(ad.address, "\n")] = 0;
    printf("\n");

    printf(" Age        : ");
    if (scanf("%i", &ad.age) != 1 || ad.age < 0 || ad.age > 150) {
        printf("Invalid age!\n");
        while (getchar() != '\n');
        menu();
        return;
    }
    printf("\n");

    while (getchar() != '\n');
    printf(" Gender (M/F): ");
    scanf("%c", &ad.gender);
    if (ad.gender != 'M' && ad.gender != 'F' && ad.gender != 'm' && ad.gender != 'f') {
        printf("Invalid gender! Use M or F.\n");
        while (getchar() != '\n');
        menu();
        return;
    }
    printf("\n");

    while (getchar() != '\n');
    printf(" Contact No : ");
    if (fgets(ad.contact, sizeof(ad.contact), stdin) == NULL) {
        printf("Error reading contact!\n");
        menu();
        return;
    }
    ad.contact[strcspn(ad.contact, "\n")] = 0;
    printf("\n");

    printf(" Room No    : ");
    if (scanf("%i", &ad.room) != 1 || ad.room < 0) {
        printf("Invalid room number!\n");
        while (getchar() != '\n');
        menu();
        return;
    }
    printf("\n");

    // Save to file
    fz = fopen(ASSIGNMENT_FILE, "a");
    if (fz) {
        fprintf(fz, "%i %s %s %i %c %s %i \n", ad.id, ad.full_name, ad.address, ad.age, ad.gender, ad.contact, ad.room);
        fclose(fz);
        printf("\n\n");
        printf(" ====== DATA RECORDED SUCCESSFULLY ====== \n\n");
    } else {
        printf("Error: Cannot save data!\n");
    }

    while (getchar() != '\n');
    getchar();
    menu();
}

void name_valid_check() {
    int valid, index;
N:
    do {
        fflush(stdin);
        // Add size limit
        if (fgets(ad.full_name, sizeof(ad.full_name), stdin) == NULL) {
            printf("Error reading input\n");
            goto N;
        }
        // Remove newline and check length
        size_t len = strlen(ad.full_name);
        if (len > 0 && ad.full_name[len-1] == '\n') {
            ad.full_name[len-1] = '\0';
            len--;
        }
        if (len >= sizeof(ad.full_name)-1) {
            printf("Name too long. Maximum %zu characters allowed\n", sizeof(ad.full_name)-1);
            goto N;
        }
        
        valid = 1;
        for (index = 0; index < len; ++index) {
            if (!isalpha(ad.full_name[index]) && !isspace(ad.full_name[index])) {
                valid = 0;
                break;
            }
        }
        if (!valid) {
            printf(" Name can only contain letters and spaces. Please enter again\n\n");
            printf(" Name       : ");
        }
    } while (!valid);
}

void p_list()
{
    system("clear");
    FILE *fz;
    fz = fopen(ASSIGNMENT_FILE, "r");
    if (!fz) {
        printf("No patient records found or cannot open file.\n");
        printf("\n\n Enter any key to return to Main Menu...");
        getchar();
        menu();
        return;
    }

    printf("\n\n\n\n%-5s |", " ID ");
    printf(" %-20s |", "Full Name");
    printf(" %-25s |", "Address");
    printf(" %-4s |", "Age");
    printf(" %-6s |", "Gender");
    printf(" %-12s |", "Phone No");
    printf("%-6s ", "Room NO");
    printf("\n================================================================================\n");

    int count = 0;
    while(fscanf(fz, "%i %49s %99s %i %c %14s %i", &ad.id, ad.full_name, ad.address, &ad.age, &ad.gender, ad.contact, &ad.room) != EOF)
    {
        printf(" %-5i|", ad.id);
        printf(" %-20s |", ad.full_name);
        printf(" %-25s |", ad.address);
        printf(" %-4i |", ad.age);
        printf(" %-6c |", ad.gender);
        printf(" %-12s |", ad.contact);
        printf(" %-6i ", ad.room);
        printf("\n");
        count++;
    }

    fclose(fz);

    if (count == 0) {
        printf("No patient records found.\n");
    } else {
        printf("\nTotal records: %d\n", count);
    }

    printf("\n\n Enter any key to return to Main Menu...");
    getchar();
    menu();
}

void find()
{
    system("clear");
    FILE *fz;
    fz = fopen(ASSIGNMENT_FILE, "r");
    if (!fz) {
        printf("Cannot open patient records file.\n");
        printf("\n\n Press any key to continue...");
        getchar();
        menu();
        return;
    }

    printf("\n Enter patient ID : ");
    if (scanf("%i", &target) != 1) {
        printf("Invalid ID format!\n");
        fclose(fz);
        while (getchar() != '\n');
        printf("\n\n Press any key to continue...");
        getchar();
        menu();
        return;
    }

    int found_record = 0;
    while(fscanf(fz, "%i %49s %99s %i %c %14s %i", &ad.id, ad.full_name, ad.address, &ad.age, &ad.gender, ad.contact, &ad.room) != EOF)
    {
        if(target == ad.id)
        {
            printf("\n Record Found.\n");
            printf("\n ID        \t: %i", ad.id);
            printf("\n Name      \t: %s", ad.full_name);
            printf("\n Address   \t: %s", ad.address);
            printf("\n Age       \t: %i", ad.age);
            printf("\n Gender    \t: %c", ad.gender);
            printf("\n Contact No\t: %s", ad.contact);
            printf("\n Room NO   \t: %i", ad.room);
            found_record = 1;
            break;
        }
    }

    if(!found_record)
    {
        printf("\n NO RECORD FOUND for ID: %d", target);
    }

    fclose(fz);

    while (getchar() != '\n');
    printf("\n\n Press any key to continue...");
    getchar();
    menu();
}

void id_valid_check()
{
    int no;  // Input ID to check
    FILE *fz;

    I:
    printf("\t\t\tID :");
    scanf("%i", &no);  // Read the ID to check first

    fz = fopen("Assignment.dat", "r");  // Changed to use relative path
    if(fz != NULL)
    {
        while(fscanf(fz, "%i %s %s %i %c %s %i", &ad.id, ad.full_name, ad.address, &ad.age, &ad.gender, ad.contact, &ad.room) != EOF)
        {
            if(no == ad.id)
            {
                printf("\n ID already exists. Please enter a different ID.");
                fclose(fz);
                goto I;
            }
        }
        fclose(fz);
        ad.id = no;  // Assign the valid ID to ad.id
    }
    else
    {
        ad.id = no;  // If file doesn't exist, first ID is valid
    }
}


void remove_record()
{
    int found =0;
    system("clear");
	FILE *fz, *tz;
	fz = fopen("d:\\Assignment.dat", "r");
	printf("\n\n\tEnter ID to remove : ");
	scanf("%i",&target);
	tz = fopen("d:\\Temp_file.dat", "w+");
	if(fz==NULL)
	{
		printf("\n\tCan not open file.");
		getchar();
		menu();
	}
	else
	{
		while(fscanf(fz,"%i %s %s %i %c %s %i\n",&ad.id,ad.full_name,ad.address,&ad.age,&ad.gender,ad.contact,&ad.room)!=EOF)
		{
			if(target!=ad.id)
			{
				fprintf(tz,"%i %s %s %i %c %s %i\n",ad.id,ad.full_name,ad.address,ad.age,ad.gender,ad.contact,ad.room);
			}
			else
			{
				found = 1;
			
			printf("\n\t\t Record Found.\n");
			printf(" ID         : %i\n",ad.id);
			printf(" Name       : %s\n",ad.full_name);
			printf(" Address    : %s\n",ad.address);
			printf(" Age        : %i\n",ad.age);
			printf(" Gender     : %c\n",ad.gender);
			printf(" Contact No : %s\n",ad.contact);
			
			break;
			}
		}
		if (!found)// if the value of found is 0
		{
			printf("\n Record not found.");
			fclose(fz);
			fclose(tz);		
		}
		else
		{
			printf("\n Record deleted.");
			fclose(fz);
			fclose(tz);
			remove("d:\\Assignment.dat");
			rename("d:\\Temp_file.dat","d:\\Assignment.dat");	
		
		}
remove("d:\\Assignment.dat");
rename("d:\\Temp_file.dat","d:\\Assignment.dat");	
		
		
		
	}
	getchar();
S:
	printf("\n\n Press esc to go back to staff menu.... ");
	int choice = getchar();
	if(choice == Esc)
	{
		menu();
	}
	else
	{
		printf("\n\n ****Invalid key.*****");
		goto S;
	}
}

void room()
{
    system("clear");
	FILE *fz;// file pointer for Shipment.txt
	fz = fopen("d:\\Assignment.dat","r");
	
	printf("\n Enter Room NO : ");
	scanf("%i",&target);
	while(fscanf(fz,"%i %s %s %i %c %s %i",&ad.id,ad.full_name,ad.address,&ad.age,&ad.gender,ad.contact,&ad.room)!=EOF)// reading the data until the end of cursor
	{
		if(target==ad.room)// if target matches the s.sno from the file then following lines are executed
		{
			
			printf("\n Room has been already reserved.");
			
			printf("\n ID        \t: %i",ad.id);
			
			printf("\n Name      \t: %s",ad.full_name);
			
			printf("\n Address   \t: %s",ad.address);
			
			printf("\n Age       \t: %i",ad.age);
			
			printf("\n Gender    \t: %c",ad.gender);
			
			printf("\n Contact No\t: %s",ad.contact);
			
		
			break;
		}
	}
	if(target!=ad.room)// if target is not found 
	{
		
		printf("\n Room is available.");
	}
	fclose(fz);// closing the file sfile
	
	printf("\n\n Press any key to continue...");
	getchar();
	menu();
}
void edit(void)
{
	system("clear");
int choice, test=0;
int select;
	FILE *fz, *tz;
fz=fopen("D:\\Assignment.dat","r");
tz=fopen("D:\\Temp_file.dat","a");

printf("\n Enter the account no. of the patient : ");
scanf("%i",&nw.id);
    while(fscanf(fz,"%i %s %s %i %c %s %i",&ad.id,ad.full_name,ad.address,&ad.age,&ad.gender,ad.contact,&ad.room)!=EOF)
    {
if (ad.id==nw.id)
        {
		test=1;
printf("\n Which information do you want to change? \n\n 1.Address \n 2.Contact number \n 3.Gender \n 4.Age \n 5.Room number \n 6.Return to menu ");
printf("\n\n Choose any number listed above : ");
			scanf("%d",&choice);
			printf("\n");

if(choice==1)
                {
				printf(" Enter the new address:");
fflush(stdin);
fgets(ad.address, sizeof(ad.address), stdin);
ad.address[strcspn(ad.address, "\n")] = 0;

printf("\n Changes saved!");

				}
else if(choice==2)
                {
printf(" Enter the new contact number:");
	fflush(stdin);
					fgets(ad.contact, sizeof(ad.contact), stdin);
ad.contact[strcspn(ad.contact, "\n")] = 0;

	printf("\n Changes saved!");
	}
else if(choice==3)
                {
printf(" Enter the new Gender : ");
fflush(stdin);
					scanf("%c",&ad.gender);
	
	
		printf("\n Changes saved!");
	}
			else if(choice==4)
                {
printf(" Enter the new age : ");
					scanf("%i",&ad.age);
	
	printf("\n Changes saved!");
	}
			else if(choice==5)
                {
printf(" Enter the new room number:");
	scanf("%i",&ad.room);
	
					printf("\n Changes saved!");
	}
			else if(choice==6)
				{
					fprintf(tz,"%i %s %s %i %c %s %i\n",ad.id,ad.full_name,ad.address,ad.age,ad.gender,ad.contact,ad.room);
					printf("\n Changing has been cancel...returning to the menu");
					getchar();
					menu();
				 }
				fprintf(tz,"%i %s %s %i %c %s %i\n",ad.id,ad.full_name,ad.address,ad.age,ad.gender,ad.contact,ad.room);
		}
else
fprintf(tz,"%i %s %s %i %c %s %i\n",ad.id,ad.full_name,ad.address,ad.age,ad.gender,ad.contact,ad.room);
    }
fclose(fz);
fclose(tz);
remove("D:\\Assignment.dat");
rename("D:\\Temp_file.dat","D:\\Assignment.dat");

if(test!=1)
        {   
printf("\nRecord not found!!\a\a\a");
            X:
printf("\n Enter 0 to try again,1 to return to main menu and 2 to exit:");
scanf("%d",&select);
if (select==1)
menu();
else if (select==2)
exitt();
else if(select==0)
edit();
else
                    {printf("\nInvalid!\a");
goto X;}
        }
else
        {printf("\n\n\n Enter 1 to go to the main menu and 2 to continue editing :");
scanf("%d",&select);

if (select==1)
menu();
else if (select==2) 
edit();
		else
			{
				
			printf(" Invalid entry!!! Exiting program..");
			getchar();
			exitt();
		}
		}
}

char* exitt() {
    char* message = "EXITING THE PROGRAM";
    exit(0);
    return message;  // Note: This return will never be reached due to exit(0)
}

// Helper: Check if a date string is today
static int is_today(const char *date_str) {
    time_t t = time(NULL);
    struct tm tm_today;
    localtime_r(&t, &tm_today);
    int y, m, d;
    if (sscanf(date_str, "%d-%d-%d", &y, &m, &d) != 3) return 0;
    return (y == tm_today.tm_year + 1900 && m == tm_today.tm_mon + 1 && d == tm_today.tm_mday);
}

int get_total_patients() {
    FILE *fz = fopen("patients.csv", "r");
    if (!fz) return 0;
    int count = 0; char buf[256];
    while (fgets(buf, sizeof(buf), fz)) count++;
    fclose(fz);
    return count > 0 ? count - 1 : 0; // Exclude header
}

int get_patients_admitted_today() {
    FILE *fz = fopen("patients.csv", "r");
    if (!fz) return 0;
    int count = 0; char buf[256];
    fgets(buf, sizeof(buf), fz); // skip header
    while (fgets(buf, sizeof(buf), fz)) {
        char *token = strtok(buf, ",");
        int col = 0;
        char date[32] = "";
        while (token) {
            if (col == 6) strncpy(date, token, sizeof(date));
            token = strtok(NULL, ",");
            col++;
        }
        if (is_today(date)) count++;
    }
    fclose(fz);
    return count;
}

int get_total_payments() {
    FILE *fz = fopen("payments.csv", "r");
    if (!fz) return 0;
    int total = 0; char buf[256];
    fgets(buf, sizeof(buf), fz); // skip header
    while (fgets(buf, sizeof(buf), fz)) {
        char *token = strtok(buf, ",");
        int col = 0, amount = 0;
        while (token) {
            if (col == 2) amount = atoi(token);
            token = strtok(NULL, ",");
            col++;
        }
        total += amount;
    }
    fclose(fz);
    return total;
}

double get_outstanding_balances() {
    FILE *fz = fopen("payments.csv", "r");
    if (!fz) return 0.0;
    double outstanding = 0.0; char buf[256];
    fgets(buf, sizeof(buf), fz); // skip header
    while (fgets(buf, sizeof(buf), fz)) {
        char *token = strtok(buf, ",");
        int col = 0; double due = 0.0;
        while (token) {
            if (col == 3) due = atof(token);
            token = strtok(NULL, ",");
            col++;
        }
        outstanding += due;
    }
    fclose(fz);
    return outstanding;
}

void find_by_name() {
    char name[20];
    int found = 0;

    printf("\nEnter the name to search: ");
    scanf("%s", name);

    FILE *file = fopen("patients.csv", "r");
    if (file == NULL) {
        printf("\nError: Unable to open file.\n");
        return;
    }

    struct customer temp;
    while (fscanf(file, "%d,%19[^,],%39[^,],%d,%c,%19[^,],%c,%9[^,],%d", &temp.id, temp.full_name, temp.address, &temp.age, &temp.gender, temp.date, &temp.time, temp.contact, &temp.room) != EOF) {
        if (strcmp(temp.full_name, name) == 0) {
            printf("\nRecord Found:\n");
            printf("ID: %d\nName: %s\nAddress: %s\nAge: %d\nGender: %c\nDate: %s\nContact: %s\nRoom: %d\n", temp.id, temp.full_name, temp.address, temp.age, temp.gender, temp.date, temp.contact, temp.room);
            found = 1;
            break;
        }
    }

    if (!found) {
        printf("\nNo record found with the name %s.\n", name);
    }

    fclose(file);
}

// Patient Management Implementation
int add_patient_record(const PatientRecord *rec) {
    FILE *fp = fopen("patients.csv", "a");
    if (!fp) return -1;

    fprintf(fp, "%d,%s,%s,%d,%c,%s,%d\n",
        rec->id, rec->full_name, rec->address, rec->age,
        rec->gender, rec->contact, rec->room);
    fclose(fp);
    return 0;
}

int delete_patient_record(int id) {
    FILE *fp = fopen("patients.csv", "r");
    FILE *temp = fopen("temp.csv", "w");
    if (!fp || !temp) return -1;

    PatientRecord rec;
    char line[256];
    int found = 0;

    while (fgets(line, sizeof(line), fp)) {
        if (sscanf(line, "%d,%[^,],%[^,],%d,%c,%[^,],%d",
            &rec.id, rec.full_name, rec.address, &rec.age,
            &rec.gender, rec.contact, &rec.room) == 7) {
            if (rec.id != id) {
                fputs(line, temp);
            } else {
                found = 1;
            }
        }
    }

    fclose(fp);
    fclose(temp);
    remove("patients.csv");
    rename("temp.csv", "patients.csv");
    return found ? 0 : -1;
}

int update_patient_record(const PatientRecord *rec) {
    FILE *fp = fopen("patients.csv", "r");
    FILE *temp = fopen("temp.csv", "w");
    if (!fp || !temp) return -1;

    PatientRecord curr;
    char line[256];
    int found = 0;

    while (fgets(line, sizeof(line), fp)) {
        if (sscanf(line, "%d,%[^,],%[^,],%d,%c,%[^,],%d",
            &curr.id, curr.full_name, curr.address, &curr.age,
            &curr.gender, curr.contact, &curr.room) == 7) {
            if (curr.id == rec->id) {
                fprintf(temp, "%d,%s,%s,%d,%c,%s,%d\n",
                    rec->id, rec->full_name, rec->address, rec->age,
                    rec->gender, rec->contact, rec->room);
                found = 1;
            } else {
                fputs(line, temp);
            }
        }
    }

    fclose(fp);
    fclose(temp);
    remove("patients.csv");
    rename("temp.csv", "patients.csv");
    return found ? 0 : -1;
}

int search_patient_record(int id, PatientRecord *rec) {
    FILE *fp = fopen(PATIENTS_FILE, "r");
    if (!fp) return -1;

    char line[256];
    int found = 0;

    while (fgets(line, sizeof(line), fp)) {
        if (sscanf(line, "%d,%49[^,],%99[^,],%d,%c,%14[^,],%d",
            &rec->id, rec->full_name, rec->address, &rec->age,
            &rec->gender, rec->contact, &rec->room) == 7) {
            if (rec->id == id) {
                found = 1;
                break;
            }
        }
    }

    fclose(fp);
    return found ? 0 : -1;
}

// Utility functions implementation
int validate_id(int id) {
    return (id > 0 && id < 999999);
}

int validate_contact(const char *contact) {
    if (!contact || strlen(contact) < 8) return 0;  // Allow shorter contacts
    int digit_count = 0;
    for (int i = 0; contact[i]; i++) {
        if (isdigit(contact[i])) {
            digit_count++;
        } else if (contact[i] != '-' && contact[i] != ' ' && contact[i] != '(' && contact[i] != ')') {
            return 0;  // Invalid character
        }
    }
    return digit_count >= 8;  // At least 8 digits required
}

int validate_name(const char *name) {
    if (!name || strlen(name) < 2) return 0;
    for (int i = 0; name[i]; i++) {
        if (!isalpha(name[i]) && name[i] != ' ' && name[i] != '.') {
            return 0;
        }
    }
    return 1;
}

void safe_string_copy(char *dest, const char *src, size_t dest_size) {
    if (!dest || !src || dest_size == 0) return;
    strncpy(dest, src, dest_size - 1);
    dest[dest_size - 1] = '\0';
}

int file_exists(const char *filename) {
    FILE *fp = fopen(filename, "r");
    if (fp) {
        fclose(fp);
        return 1;
    }
    return 0;
}

int validate_patient_record(const PatientRecord *rec) {
    if (!rec) return 0;
    if (!validate_id(rec->id)) return 0;
    if (!validate_name(rec->full_name)) return 0;
    if (rec->age < 0 || rec->age > 150) return 0;
    if (rec->gender != 'M' && rec->gender != 'F' && rec->gender != 'm' && rec->gender != 'f') return 0;
    if (!validate_contact(rec->contact)) return 0;
    if (rec->room < 0) return 0;
    return 1;
}

void list_patient_records(void) {
    p_list(); // Use existing implementation
}
