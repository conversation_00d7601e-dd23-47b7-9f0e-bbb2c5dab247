#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include "../inc/functions.h"
#include "../inc/dashboard.h"

// Calculate beds statistics
void calculate_bed_statistics(DashboardStats *stats) {
    FILE *fp = fopen("wards.dat", "rb");
    if (!fp) return;

    WardRecord ward;
    stats->total_beds = 0;
    stats->occupied_beds = 0;

    while (fread(&ward, sizeof(WardRecord), 1, fp)) {
        stats->total_beds += ward.total_beds;
        stats->occupied_beds += ward.occupied_beds;
    }
    fclose(fp);

    stats->available_beds = stats->total_beds - stats->occupied_beds;
    if (stats->total_beds > 0) {
        stats->bed_occupancy_rate = ((float)stats->occupied_beds / stats->total_beds) * 100;
    } else {
        stats->bed_occupancy_rate = 0;
    }
}

// Calculate patient statistics
void calculate_patient_statistics(DashboardStats *stats) {
    // Get total patients
    stats->total_patients = get_total_patients();

    // Count admitted and outpatients
    FILE *fp = fopen(DATA_FILE, "rb");
    if (!fp) return;

    PatientRecord patient;
    stats->admitted_patients = 0;
    stats->outpatients = 0;

    while (fread(&patient, sizeof(PatientRecord), 1, fp)) {
        if (patient.room > 0) {
            stats->admitted_patients++;
        } else {
            stats->outpatients++;
        }
    }
    fclose(fp);

    // Get today's admissions
    char today[11];
    time_t now = time(NULL);
    strftime(today, sizeof(today), "%Y-%m-%d", localtime(&now));

    stats->new_registrations = 0;
    fp = fopen("admissions.log", "r");
    if (fp) {
        char date[11];
        while (fscanf(fp, "%10s", date) == 1) {
            if (strcmp(date, today) == 0) {
                stats->new_registrations++;
            }
        }
        fclose(fp);
    }
}

// Calculate emergency statistics
void calculate_emergency_statistics(DashboardStats *stats) {
    char today[11];
    time_t now = time(NULL);
    strftime(today, sizeof(today), "%Y-%m-%d", localtime(&now));

    FILE *fp = fopen("emergency.log", "r");
    if (!fp) {
        stats->emergency_cases = 0;
        stats->critical_patients = 0;
        return;
    }

    stats->emergency_cases = 0;
    stats->critical_patients = 0;

    char date[11], type[20];
    while (fscanf(fp, "%10s %19s", date, type) == 2) {
        if (strcmp(date, today) == 0) {
            stats->emergency_cases++;
            if (strcmp(type, "CRITICAL") == 0) {
                stats->critical_patients++;
            }
        }
    }
    fclose(fp);
}

// Calculate financial statistics
void calculate_financial_statistics(DashboardStats *stats) {
    char today[11];
    time_t now = time(NULL);
    strftime(today, sizeof(today), "%Y-%m-%d", localtime(&now));

    FILE *fp = fopen("payments.csv", "r");
    if (!fp) {
        stats->total_revenue = 0;
        stats->pending_payments = 0;
        stats->today_collections = 0;
        return;
    }

    stats->total_revenue = 0;
    stats->pending_payments = 0;
    stats->today_collections = 0;

    char line[256];
    while (fgets(line, sizeof(line), fp)) {
        char date[11];
        float amount;
        char status[20];

        if (sscanf(line, "%10[^,],%f,%*[^,],%19s", date, &amount, status) == 3) {
            stats->total_revenue += amount;

            if (strcmp(status, "Pending") == 0) {
                stats->pending_payments += amount;
            }

            if (strcmp(date, today) == 0 && strcmp(status, "Paid") == 0) {
                stats->today_collections += amount;
            }
        }
    }
    fclose(fp);

    // Calculate insurance claims
    fp = fopen("insurance_claims.csv", "r");
    if (fp) {
        stats->insurance_claims = 0;
        float amount;
        char status[20];

        while (fscanf(fp, "%*[^,],%f,%19s", &amount, status) == 2) {
            if (strcmp(status, "Pending") == 0) {
                stats->insurance_claims += amount;
            }
        }
        fclose(fp);
    }
}

// Save statistics to file for historical tracking
void save_daily_statistics(const DashboardStats *stats) {
    char date[11];
    time_t now = time(NULL);
    strftime(date, sizeof(date), "%Y-%m-%d", localtime(&now));

    FILE *fp = fopen("statistics_history.csv", "a");
    if (!fp) return;

    fprintf(fp, "%s,%d,%d,%d,%d,%.2f,%.2f,%.2f,%d,%d\n",
        date,
        stats->total_patients,
        stats->admitted_patients,
        stats->outpatients,
        stats->new_registrations,
        stats->total_revenue,
        stats->pending_payments,
        stats->bed_occupancy_rate,
        stats->emergency_cases,
        stats->critical_patients
    );

    fclose(fp);
}
