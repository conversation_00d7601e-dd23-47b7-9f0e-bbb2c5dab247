<?xml version="1.0" encoding="UTF-8"?>
<interface>
  <requires lib="gtk+" version="3.20"/>

  <!-- Login Window -->
  <object class="GtkWindow" id="login_window">
    <property name="title">Hospital Management System - Login</property>
    <property name="default_width">500</property>
    <property name="default_height">500</property>
    <property name="resizable">False</property>
    <child>
      <object class="GtkBox">
        <property name="visible">True</property>
        <property name="orientation">vertical</property>
        <property name="spacing">10</property>
        <property name="margin">20</property>
        <child>
          <object class="GtkLabel">
            <property name="visible">True</property>
            <property name="label">Login to Hospital Management System</property>
          </object>
        </child>
        <child>
          <object class="GtkEntry" id="username_entry">
            <property name="visible">True</property>
            <property name="placeholder_text">Username</property>
          </object>
        </child>
        <child>
          <object class="GtkEntry" id="password_entry">
            <property name="visible">True</property>
            <property name="visibility">False</property>
            <property name="placeholder_text">Password</property>
          </object>
        </child>
        <child>
          <object class="GtkButton" id="login_button">
            <property name="visible">True</property>
            <property name="label">Login</property>
          </object>
        </child>
      </object>
    </child>
  </object>

  <!-- Main Window -->
  <object class="GtkWindow" id="main_window">
    <property name="title">Hospital Management System</property>
    <property name="default_width">800</property>
    <property name="default_height">600</property>
    <child>
      <object class="GtkBox">
        <property name="visible">True</property>
        <property name="orientation">vertical</property>
        <property name="spacing">10</property>
        <child>
          <object class="GtkMenuBar" id="menubar">
            <property name="visible">True</property>
            <child>
              <object class="GtkMenuItem">
                <property name="visible">True</property>
                <property name="label">File</property>
                <child type="submenu">
                  <object class="GtkMenu">
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkMenuItem" id="menu_logout">
                        <property name="visible">True</property>
                        <property name="label">Logout</property>
                      </object>
                    </child>
                    <child>
                      <object class="GtkMenuItem" id="menu_exit">
                        <property name="visible">True</property>
                        <property name="label">Exit</property>
                      </object>
                    </child>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>

        <child>
          <object class="GtkNotebook" id="main_notebook">
            <property name="visible">True</property>

            <!-- Patient Management Tab -->
            <child>
              <object class="GtkGrid" id="patient_grid">
                <property name="visible">True</property>
                <property name="margin">10</property>
                <property name="row_spacing">10</property>
                <property name="column_spacing">10</property>
                <child>
                  <object class="GtkButton" id="btn_add_patient">
                    <property name="visible">True</property>
                    <property name="label">Add Patient</property>
                  </object>
                </child>
                <child>
                  <object class="GtkButton" id="btn_view_patients">
                    <property name="visible">True</property>
                    <property name="label">View Patients</property>
                  </object>
                </child>
                <child>
                  <object class="GtkButton" id="btn_search_patient">
                    <property name="visible">True</property>
                    <property name="label">Search Patient</property>
                  </object>
                </child>
                <child>
                  <object class="GtkButton" id="btn_update_patient">
                    <property name="visible">True</property>
                    <property name="label">Update Patient</property>
                  </object>
                </child>
                <child>
                  <object class="GtkButton" id="btn_delete_patient">
                    <property name="visible">True</property>
                    <property name="label">Delete Patient</property>
                  </object>
                </child>
              </object>
            </child>
            <child type="tab">
              <object class="GtkLabel">
                <property name="visible">True</property>
                <property name="label">Patient Management</property>
              </object>
            </child>

            <!-- Doctor Management Tab -->
            <child>
              <object class="GtkGrid" id="doctor_grid">
                <property name="visible">True</property>
                <property name="margin">10</property>
                <property name="row_spacing">10</property>
                <property name="column_spacing">10</property>
                <child>
                  <object class="GtkButton" id="btn_add_doctor">
                    <property name="visible">True</property>
                    <property name="label">Add Doctor</property>
                  </object>
                </child>
                <child>
                  <object class="GtkButton" id="btn_view_doctors">
                    <property name="visible">True</property>
                    <property name="label">View Doctors</property>
                  </object>
                </child>
                <child>
                  <object class="GtkButton" id="btn_search_doctor">
                    <property name="visible">True</property>
                    <property name="label">Search Doctor</property>
                  </object>
                </child>
                <child>
                  <object class="GtkButton" id="btn_update_doctor">
                    <property name="visible">True</property>
                    <property name="label">Update Doctor</property>
                  </object>
                </child>
                <child>
                  <object class="GtkButton" id="btn_delete_doctor">
                    <property name="visible">True</property>
                    <property name="label">Delete Doctor</property>
                  </object>
                </child>
              </object>
            </child>
            <child type="tab">
              <object class="GtkLabel">
                <property name="visible">True</property>
                <property name="label">Doctor Management</property>
              </object>
            </child>

            <!-- Staff Management Tab -->
            <child>
              <object class="GtkGrid" id="staff_grid">
                <property name="visible">True</property>
                <property name="margin">10</property>
                <property name="row_spacing">10</property>
                <property name="column_spacing">10</property>
                <child>
                  <object class="GtkButton" id="btn_add_staff">
                    <property name="visible">True</property>
                    <property name="label">Add Staff</property>
                  </object>
                </child>
                <child>
                  <object class="GtkButton" id="btn_view_staff">
                    <property name="visible">True</property>
                    <property name="label">View Staff</property>
                  </object>
                </child>
                <child>
                  <object class="GtkButton" id="btn_search_staff">
                    <property name="visible">True</property>
                    <property name="label">Search Staff</property>
                  </object>
                </child>
                <child>
                  <object class="GtkButton" id="btn_update_staff">
                    <property name="visible">True</property>
                    <property name="label">Update Staff</property>
                  </object>
                </child>
                <child>
                  <object class="GtkButton" id="btn_delete_staff">
                    <property name="visible">True</property>
                    <property name="label">Delete Staff</property>
                  </object>
                </child>
              </object>
            </child>
            <child type="tab">
              <object class="GtkLabel">
                <property name="visible">True</property>
                <property name="label">Staff Management</property>
              </object>
            </child>

            <!-- Ward Management Tab -->
            <child>
              <object class="GtkGrid" id="ward_grid">
                <property name="visible">True</property>
                <property name="margin">10</property>
                <property name="row_spacing">10</property>
                <property name="column_spacing">10</property>
                <child>
                  <object class="GtkButton" id="btn_add_ward">
                    <property name="visible">True</property>
                    <property name="label">Add Ward</property>
                  </object>
                </child>
                <child>
                  <object class="GtkButton" id="btn_view_wards">
                    <property name="visible">True</property>
                    <property name="label">View Wards</property>
                  </object>
                </child>
                <child>
                  <object class="GtkButton" id="btn_search_ward">
                    <property name="visible">True</property>
                    <property name="label">Search Ward</property>
                  </object>
                </child>
                <child>
                  <object class="GtkButton" id="btn_update_ward">
                    <property name="visible">True</property>
                    <property name="label">Update Ward</property>
                  </object>
                </child>
                <child>
                  <object class="GtkButton" id="btn_delete_ward">
                    <property name="visible">True</property>
                    <property name="label">Delete Ward</property>
                  </object>
                </child>
              </object>
            </child>
            <child type="tab">
              <object class="GtkLabel">
                <property name="visible">True</property>
                <property name="label">Ward Management</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkDrawingArea" id="visualization_area">
            <property name="visible">True</property>
            <property name="width_request">600</property>
            <property name="height_request">400</property>
          </object>
        </child>
      </object>
    </child>
  </object>
</interface>
