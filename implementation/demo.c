#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "inc/functions.h"

void demo_patient_management() {
    printf("\n=== PATIENT MANAGEMENT DEMO ===\n");
    
    // Create sample patient records
    PatientRecord patients[] = {
        {1001, "<PERSON>", "123 Main St, City", 35, 'M', "555-0123", 101},
        {1002, "Jane Smith", "456 Oak Ave, Town", 28, 'F', "555-0456", 102},
        {1003, "<PERSON>", "789 Pine Rd, Village", 45, 'M', "555-0789", 0}
    };
    
    printf("Adding sample patients...\n");
    for (int i = 0; i < 3; i++) {
        if (validate_patient_record(&patients[i])) {
            if (add_patient_record(&patients[i]) == 0) {
                printf("✓ Added patient: %s (ID: %d)\n", patients[i].full_name, patients[i].id);
            } else {
                printf("✗ Failed to add patient: %s\n", patients[i].full_name);
            }
        } else {
            printf("✗ Invalid patient record: %s\n", patients[i].full_name);
        }
    }
    
    // Search for a patient
    printf("\nSearching for patient ID 1002...\n");
    PatientRecord found_patient;
    if (search_patient_record(1002, &found_patient) == 0) {
        printf("✓ Found patient: %s, Age: %d, Room: %d\n", 
               found_patient.full_name, found_patient.age, found_patient.room);
    } else {
        printf("✗ Patient not found\n");
    }
    
    // Update patient room
    printf("\nUpdating patient room...\n");
    found_patient.room = 205;
    if (update_patient_record(&found_patient) == 0) {
        printf("✓ Updated patient room to: %d\n", found_patient.room);
    } else {
        printf("✗ Failed to update patient\n");
    }
}

void demo_validation_functions() {
    printf("\n=== VALIDATION FUNCTIONS DEMO ===\n");
    
    // Test ID validation
    printf("Testing ID validation:\n");
    printf("  ID 1001: %s\n", validate_id(1001) ? "Valid" : "Invalid");
    printf("  ID -1: %s\n", validate_id(-1) ? "Valid" : "Invalid");
    printf("  ID 0: %s\n", validate_id(0) ? "Valid" : "Invalid");
    
    // Test name validation
    printf("\nTesting name validation:\n");
    printf("  'John Doe': %s\n", validate_name("John Doe") ? "Valid" : "Invalid");
    printf("  'John123': %s\n", validate_name("John123") ? "Valid" : "Invalid");
    printf("  'J': %s\n", validate_name("J") ? "Valid" : "Invalid");
    
    // Test contact validation
    printf("\nTesting contact validation:\n");
    printf("  '555-0123': %s\n", validate_contact("555-0123") ? "Valid" : "Invalid");
    printf("  '**********': %s\n", validate_contact("**********") ? "Valid" : "Invalid");
    printf("  '123': %s\n", validate_contact("123") ? "Valid" : "Invalid");
}

void demo_appointment_management() {
    printf("\n=== APPOINTMENT MANAGEMENT DEMO ===\n");
    
    // Create sample appointments
    AppointmentRecord appointments[] = {
        {2001, 1001, 3001, "2024-06-08", "09:00", "Scheduled", "Regular checkup"},
        {2002, 1002, 3002, "2024-06-08", "10:30", "Scheduled", "Follow-up visit"},
        {2003, 1003, 3001, "2024-06-09", "14:00", "Scheduled", "Consultation"}
    };
    
    printf("Adding sample appointments...\n");
    for (int i = 0; i < 3; i++) {
        if (add_appointment_record(&appointments[i]) == 0) {
            printf("✓ Added appointment ID: %d for patient %d\n", 
                   appointments[i].id, appointments[i].patient_id);
        } else {
            printf("✗ Failed to add appointment ID: %d\n", appointments[i].id);
        }
    }
    
    // Search for an appointment
    printf("\nSearching for appointment ID 2001...\n");
    AppointmentRecord found_appointment;
    if (search_appointment_record(2001, &found_appointment) == 0) {
        printf("✓ Found appointment: Patient %d with Doctor %d on %s at %s\n",
               found_appointment.patient_id, found_appointment.doctor_id,
               found_appointment.date, found_appointment.time);
    } else {
        printf("✗ Appointment not found\n");
    }
    
    printf("\nTotal appointments: %d\n", get_total_appointments());
}

void demo_insurance_management() {
    printf("\n=== INSURANCE MANAGEMENT DEMO ===\n");
    
    // Create sample insurance records
    InsuranceRecord insurance[] = {
        {1001, "HealthCare Plus", "HC123456789", 50000.0, "2025-12-31"},
        {1002, "MediCare Pro", "MC987654321", 75000.0, "2024-08-15"},
        {1003, "Family Health", "FH456789123", 30000.0, "2025-06-30"}
    };
    
    printf("Adding sample insurance records...\n");
    for (int i = 0; i < 3; i++) {
        if (add_insurance_record(&insurance[i]) == 0) {
            printf("✓ Added insurance for patient %d: %s\n", 
                   insurance[i].patient_id, insurance[i].provider);
        } else {
            printf("✗ Failed to add insurance for patient %d\n", insurance[i].patient_id);
        }
    }
    
    // Get patient insurance
    printf("\nGetting insurance for patient 1001...\n");
    InsuranceRecord patient_insurance;
    if (get_patient_insurance(1001, &patient_insurance) == 0) {
        printf("✓ Found insurance: %s, Coverage: $%.2f, Expires: %s\n",
               patient_insurance.provider, patient_insurance.coverage_amount,
               patient_insurance.expiry_date);
    } else {
        printf("✗ No insurance found for patient\n");
    }
    
    // Calculate insurance claim
    double bill_amount = 1500.0;
    double claim = calculate_insurance_claim(1001, bill_amount);
    printf("\nInsurance claim calculation:\n");
    printf("  Bill amount: $%.2f\n", bill_amount);
    printf("  Insurance claim: $%.2f\n", claim);
    printf("  Patient pays: $%.2f\n", bill_amount - claim);
}

void demo_statistics() {
    printf("\n=== STATISTICS DEMO ===\n");
    
    printf("System Statistics:\n");
    printf("  Total patients: %d\n", get_total_patients());
    printf("  Total doctors: %d\n", get_total_doctors());
    printf("  Total staff: %d\n", get_total_staff());
    printf("  Total wards: %d\n", get_total_wards());
    printf("  Available beds: %d\n", get_available_beds());
    printf("  Total appointments: %d\n", get_total_appointments());
    printf("  Total payments: %d\n", get_total_payments());
    printf("  Outstanding balances: $%.2f\n", get_outstanding_balances());
    printf("  Total insurance coverage: $%.2f\n", get_total_insurance_coverage());
}

int main() {
    printf("=================================================\n");
    printf("    HOSPITAL MANAGEMENT SYSTEM DEMONSTRATION\n");
    printf("=================================================\n");
    
    // Run demonstrations
    demo_validation_functions();
    demo_patient_management();
    demo_appointment_management();
    demo_insurance_management();
    demo_statistics();
    
    printf("\n=================================================\n");
    printf("           DEMONSTRATION COMPLETED\n");
    printf("=================================================\n");
    printf("\nAll core functionalities have been demonstrated.\n");
    printf("The system is ready for production use!\n\n");
    
    printf("To run the GUI application:\n");
    printf("  ./build/hospital.out\n\n");
    
    printf("To run tests:\n");
    printf("  make test\n\n");
    
    return 0;
}
