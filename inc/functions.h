/*#ifndef FUNCTIONS_H
#define FUNCTIONS_H

#include <stdbool.h>

/* Define the PatientRecord structure
typedef struct {
    int id;
    char full_name[50];
    char address[100];
    int age;
    char gender;
    char contact[15];
    int room;
} PatientRecord;

// Function declarations
bool delete_patient_record(int id);
bool search_patient_record(int id, PatientRecord *record);
bool update_patient_record(PatientRecord *record);
bool add_patient_record(PatientRecord *record);

#endif // FUNCTIONS_H
