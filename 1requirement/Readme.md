# Requirements
## Introduction
 This project is the implementation for “ HOSPITAL MANAGEMENT SYSTEM” is a program developed for managing details regarding number of patients entering to the hospital. The program is very helpful to find the results of the inpatients and outpatients easily. we can search the doctors related to patients in hospital demo. The project and implementation is developed by using c.  There’s  login system available for this system, the receptionist login into the system and use resources.

## Research

About Hospital Management System C Project:

---Add new patient record: In this feature, user can add a new patient record
---Search or edit patient record.
---List record of patients.
---Delete patient records'
---Exit from the system
 
## Cost and Features and Timeline

1.Time-saving Technology
2.Improved Efficiency by avoiding human errors
3.Reduces scope for Error
4.Data security and correct data retrieval made possible
5.Cost effective and easily manageable
6.Easy access to patient data with correct patient history
7.Improved patient care made possible
8.Reduces the work of documentation

## Defining Our System
    Here in our project(Hospital management system) user can able to login and should enter password then main menu will be display .The login password would be declared default.
    Now user can add new patient record,delete existing patient record, update existing records, view list existing records etc. Finally user can exit from system.
## SWOT ANALYSIS
<h3>Strengths

•	A primary focus on quality improvement ($ saved from quality projects)
•	Internal teams dedicated to research and analytics ($ spent on research)
•	High-quality medical personnel (# of staff with external recognition)
•	Modern medical equipment and a well-equipped facility (average age of medical equipment)

<h3>Weakness

•	Outdated healthcare facilities and technology
•	Insufficient management training
•	Lack of funding and resources to support programs
•	Poor location that’s not easily accessible for staff and patients
•	High staff turnover

<h3>Opportunities:

•	Collaborate with different healthcare organizations to knowledge share
•	Develop healthcare programs and initiatives to drive more community outreach and engagement
•	Increase funding for analytics and researching, including both staff and technology
•	Create mentor programs

<h3>Threats:

•	Economic or political insecurity
•	Policy and legislation changes that result in budget deficits
•	Pressure to reduce costs while meeting expectations of universal healthcare coverage
•	Increased competition from newly built hospitals

## Who
The implementation of hospital management system project provides the institution with different advantages that improve the service quality and efficiency. As mentioned above it is created a user can handle: patients details, recordsf and management, and third-parties like drug suppliers and insurance companies.

## What
The hospital management system helps register complete patient information. It captures and stores the medical history, treatment required, details of their previous visits, upcoming appointments if any, reports, insurance details and more. It helps eliminate the need to get these details on every visit.

## When
HMS information system helps to track and control finances, reduce leakages as well as reduce manual work and therefore there is no requirement of the higher human workforce.

## Where
Hospital Management System helps the professionals in healthcare to check out the clinical documents, diagnosis, patient records, and other relevant things at a single view and therefore results in the healthcare professionals to make decisions at the right time

## How
    The system creates an external file to store the data perminantly.This system is deployed using c programming.

## High Level Requirements
| **<h3> ID** | **<h3>Description**                                              | <h3>Status      |    
|-------------|--------------------------------------------------------------|------------- |
 | Patient Activities|-It includes patient records,patient information  | Implemented    |
| Adminstration     |It includes room informtion,general adminstration| Implemented    |
|  Payment Details       |It includes patient payment details,pay recipts etc| future    |
##  Low level Requirements
 | **<h3> ID** | **<h3>Description**                                              | <h3>Status      |    
|-------------|--------------------------------------------------------------|------------- |
 | security|It should secure because  only authorized user or receptionist can this system  | Implemented    |
| platform     |The system can be work on any operating system like windows or linus| Implemented    |
|  Performanace       |The system should handles the large amount patients data| Implemented    |

## Enhancements and Advanced Features

### New Features in the Enhanced Hospital Management System

- **Graphical User Interface (GUI):**
  - The system now includes a modern GUI built with GTK for Linux, making it more user-friendly and accessible.
  - All major features (Add, Delete, Search, View, Update, Room management) are accessible via buttons and dialogs.

- **Advanced Search:**
  - Search for patients not only by ID but also by full or partial name (case-insensitive), improving usability for large datasets.

- **View All Records:**
  - Easily view all patient records in a scrollable window within the GUI.

- **Input Validation:**
  - Enhanced input validation in the GUI ensures all required fields are filled and data is consistent.

- **Cross-Platform Compatibility:**
  - The system can be run in both terminal (CLI) and GUI modes, supporting a wider range of users and environments.

- **Modular Codebase:**
  - The code is organized for easy extension, allowing new features to be added with minimal changes.

### Planned/Optional Future Enhancements
- Export patient data to CSV for use in Excel or other tools.
- User authentication with multiple roles (admin, staff).
- Sorting and filtering of records in the GUI.
- Colorful and accessible interface with improved error messages.
- Integration with external systems (e.g., insurance, pharmacy).

### Implemented Enhancements

- **Export to CSV:**
  - You can now export all patient data to a CSV file for use in Excel or other tools, directly from the GUI.

- **User Authentication with Multiple Roles:**
  - Planned: The system will support login for both admin and staff roles, with different permissions (e.g., only admin can delete records).

- **Sorting and Filtering in the GUI:**
  - Planned: The GUI will allow sorting and filtering of patient records by name, age, or room for easier management.

---

## How to Use the Enhanced System

- **Terminal Mode:**
  - Compile and run as before for a text-based interface.
- **GUI Mode:**
  - Install GTK 3 (`sudo apt-get install libgtk-3-dev`)
  - Compile with: `gcc gui.c -o gui_app `pkg-config --cflags --libs gtk+-3.0``
  - Run with: `./gui_app`

---
