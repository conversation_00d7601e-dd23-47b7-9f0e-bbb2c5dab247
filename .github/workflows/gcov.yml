name: CI-Coverage

# Controls when the action will run. 
on:
  # Triggers the workflow on push or pull request events but only for the main branch
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest


    steps:
    - uses: actions/checkout@v2
    - name: Install gcov 
      run: sudo apt -y install gcovr
    - name: make coverage
      run: make -C implementation/ coverage
    - name: GCov only
      run:  gcovr -r .
